#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全局无AK认证初始化
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_global_akless_init():
    """测试全局无AK认证初始化"""
    print("=== 测试全局无AK认证初始化 ===")
    
    # 设置测试环境
    os.environ["ENV_FOR_DYNACONF"] = "daily"
    
    try:
        # 1. 测试应用启动初始化
        from src.shared.startup import initialize_app
        print("1. 执行应用启动初始化...")
        result = initialize_app()
        print(f"   初始化结果: {result}")
        
        # 2. 测试无AK认证状态
        from src.shared.auth import is_akless_initialized, get_initialization_error
        print(f"2. 无AK认证初始化状态: {is_akless_initialized()}")
        error = get_initialization_error()
        if error:
            print(f"   初始化错误: {error}")
        
        # 3. 测试客户端创建（使用全局认证）
        print("3. 测试客户端创建...")
        try:
            from src.popclients.rag_client import RagClient
            client = RagClient()
            print("   ✅ RagClient 创建成功")
            print(f"   客户端信息: {client.get_client_info()}")
        except Exception as e:
            print(f"   ❌ RagClient 创建失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试全局无AK认证初始化...")
    test_global_akless_init()
    print("\n=== 测试完成 ===")
    print("注意：在本地环境中，无AK认证可能会失败，这是正常的。")
    print("实际部署时，请确保在应用启动时调用 initialize_app() 函数。")

if __name__ == "__main__":
    main()
