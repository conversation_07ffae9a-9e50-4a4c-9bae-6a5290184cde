#!/usr/bin/env python3
"""
测试 /api/sessions/send 接口
特别测试传递 desktopId 的情况
"""

import sys
import os
import json
import asyncio
import httpx
from typing import Optional, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger


class SessionSendAPITester:
    """会话发送API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", login_token: str = None):
        """
        初始化测试器
        
        Args:
            base_url: API服务器地址
            login_token: 登录令牌（留空，用户可以自己填写）
        """
        self.base_url = base_url.rstrip('/')
        self.login_token = login_token or "YOUR_LOGIN_TOKEN_HERE"  # 用户需要填写
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.login_token}",
            "loginToken": self.login_token,
            "sessionId": "test_session_id",
            "regionId": "cn-hangzhou"
        }
        
    async def send_message(
        self,
        prompt: str,
        agent_id: str,
        session_id: Optional[str] = None,
        desktop_id: Optional[str] = None,
        auth_code: Optional[str] = None,
        resources: Optional[list] = None
    ) -> Dict[str, Any]:
        """发送消息到会话"""
        url = f"{self.base_url}/api/sessions/send"
        
        # 构建请求数据
        data = {
            "Prompt": prompt,
            "AgentId": agent_id
        }
        
        if session_id:
            data["SessionId"] = session_id
        if desktop_id:
            data["DesktopId"] = desktop_id
        if auth_code:
            data["AuthCode"] = auth_code
        if resources:
            data["Resources"] = resources
            
        logger.info(f"发送请求到: {url}")
        logger.info(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, headers=self.headers, json=data)
            
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            
            try:
                response_data = response.json()
                logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                return response_data
            except Exception as e:
                logger.error(f"解析响应JSON失败: {e}")
                logger.info(f"原始响应: {response.text}")
                return {"error": "JSON解析失败", "raw_response": response.text}


async def test_send_message_with_desktop_id():
    """测试带 desktop_id 的消息发送"""
    logger.info("=== 测试带 desktop_id 的消息发送 ===")
    
    try:
        tester = SessionSendAPITester()
        
        # 测试参数
        test_cases = [
            {
                "name": "基本桌面ID测试",
                "prompt": "请帮我分析一下当前桌面环境",
                "agent_id": "desktop_agent",
                "desktop_id": "desktop_001",
                "auth_code": "test_auth_code_123",
                "description": "测试基本的桌面ID传递"
            },
            {
                "name": "AgentBay桌面测试",
                "prompt": "启动AgentBay环境",
                "agent_id": "agentbay_agent",
                "desktop_id": "agentbay",
                "auth_code": "agentbay_auth_456",
                "description": "测试AgentBay特殊桌面ID"
            },
            {
                "name": "长桌面ID测试",
                "prompt": "连接到指定的桌面实例",
                "agent_id": "desktop_manager",
                "desktop_id": "desktop_very_long_id_with_special_chars_123456789",
                "auth_code": "complex_auth_code_789",
                "description": "测试长桌面ID的处理"
            },
            {
                "name": "无桌面ID测试",
                "prompt": "这是一个不需要桌面的对话",
                "agent_id": "chat_agent",
                "desktop_id": None,
                "auth_code": None,
                "description": "测试不传递桌面ID的情况"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n{i}. {test_case['name']}")
            logger.info(f"   描述: {test_case['description']}")
            logger.info(f"   Prompt: {test_case['prompt']}")
            logger.info(f"   Agent ID: {test_case['agent_id']}")
            logger.info(f"   Desktop ID: {test_case['desktop_id']}")
            logger.info(f"   Auth Code: {test_case['auth_code']}")
            
            try:
                result = await tester.send_message(
                    prompt=test_case['prompt'],
                    agent_id=test_case['agent_id'],
                    desktop_id=test_case['desktop_id'],
                    auth_code=test_case['auth_code']
                )
                
                # 分析结果
                if isinstance(result, dict):
                    if result.get("Code") == 200 or result.get("code") == 200:
                        logger.success(f"   ✅ 测试成功")
                        data = result.get("Data") or result.get("data")
                        if data:
                            session_id = data.get("SessionId") or data.get("session_id")
                            round_id = data.get("RoundId") or data.get("round_id")
                            logger.info(f"   Session ID: {session_id}")
                            logger.info(f"   Round ID: {round_id}")
                    else:
                        logger.error(f"   ❌ 测试失败: {result.get('Msg') or result.get('msg')}")
                else:
                    logger.error(f"   ❌ 响应格式异常: {result}")
                
                results.append({
                    "test_case": test_case['name'],
                    "success": True,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"   ❌ 测试异常: {e}")
                results.append({
                    "test_case": test_case['name'],
                    "success": False,
                    "error": str(e)
                })
            
            # 测试间隔
            await asyncio.sleep(1)
        
        # 总结
        logger.info(f"\n=== 测试总结 ===")
        success_count = sum(1 for r in results if r['success'])
        logger.info(f"总测试数: {len(results)}")
        logger.info(f"成功数: {success_count}")
        logger.info(f"失败数: {len(results) - success_count}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        return []


async def test_send_message_with_resources():
    """测试带资源的消息发送"""
    logger.info("=== 测试带资源的消息发送 ===")
    
    try:
        tester = SessionSendAPITester()
        
        # 构建测试资源
        test_resources = [
            {
                "Type": "file",
                "Id": "file_001",
                "Name": "测试文档.pdf",
                "Url": "https://example.com/files/test.pdf"
            },
            {
                "Type": "image",
                "Id": "image_001", 
                "Name": "截图.png",
                "Url": "https://example.com/images/screenshot.png"
            }
        ]
        
        result = await tester.send_message(
            prompt="请分析这些文件内容",
            agent_id="file_analyzer",
            desktop_id="desktop_002",
            auth_code="file_auth_123",
            resources=test_resources
        )
        
        logger.info("资源测试完成")
        return result
        
    except Exception as e:
        logger.error(f"❌ 资源测试异常: {e}")
        return None


async def test_send_message_session_continuation():
    """测试会话延续"""
    logger.info("=== 测试会话延续 ===")
    
    try:
        tester = SessionSendAPITester()
        
        # 第一条消息 - 创建新会话
        logger.info("1. 发送第一条消息（创建新会话）")
        first_result = await tester.send_message(
            prompt="你好，我想开始一个新的对话",
            agent_id="chat_agent",
            desktop_id="desktop_003",
            auth_code="session_auth_456"
        )
        
        session_id = None
        if isinstance(first_result, dict):
            data = first_result.get("Data") or first_result.get("data")
            if data:
                session_id = data.get("SessionId") or data.get("session_id")
                logger.info(f"   创建的会话ID: {session_id}")
        
        if not session_id:
            logger.error("   ❌ 未能获取会话ID")
            return None
        
        # 等待一下
        await asyncio.sleep(2)
        
        # 第二条消息 - 延续会话
        logger.info("2. 发送第二条消息（延续会话）")
        second_result = await tester.send_message(
            prompt="请继续我们刚才的对话",
            agent_id="chat_agent",
            session_id=session_id,  # 使用相同的会话ID
            desktop_id="desktop_003",
            auth_code="session_auth_456"
        )
        
        logger.info("会话延续测试完成")
        return {
            "first_result": first_result,
            "second_result": second_result,
            "session_id": session_id
        }
        
    except Exception as e:
        logger.error(f"❌ 会话延续测试异常: {e}")
        return None


async def test_error_cases():
    """测试错误情况"""
    logger.info("=== 测试错误情况 ===")
    
    try:
        tester = SessionSendAPITester()
        
        error_cases = [
            {
                "name": "缺少必填参数",
                "data": {
                    "AgentId": "test_agent"
                    # 缺少 Prompt
                },
                "description": "测试缺少必填的Prompt参数"
            },
            {
                "name": "空的Prompt",
                "data": {
                    "Prompt": "",
                    "AgentId": "test_agent",
                    "DesktopId": "desktop_004"
                },
                "description": "测试空的Prompt参数"
            },
            {
                "name": "无效的Agent ID",
                "data": {
                    "Prompt": "测试消息",
                    "AgentId": "",
                    "DesktopId": "desktop_005"
                },
                "description": "测试空的Agent ID"
            }
        ]
        
        for i, case in enumerate(error_cases, 1):
            logger.info(f"\n{i}. {case['name']}")
            logger.info(f"   描述: {case['description']}")
            
            try:
                url = f"{tester.base_url}/api/sessions/send"
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(url, headers=tester.headers, json=case['data'])
                    
                    logger.info(f"   状态码: {response.status_code}")
                    
                    if response.status_code >= 400:
                        logger.success(f"   ✅ 正确返回错误状态码")
                    else:
                        logger.warning(f"   ⚠️ 未返回预期的错误状态码")
                    
                    try:
                        result = response.json()
                        logger.info(f"   错误响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    except:
                        logger.info(f"   原始响应: {response.text}")
                        
            except Exception as e:
                logger.info(f"   请求异常（预期）: {e}")
        
        logger.info("错误情况测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 错误情况测试异常: {e}")
        return False


def print_usage_instructions():
    """打印使用说明"""
    logger.info("=== 使用说明 ===")
    logger.info("1. 请确保API服务器正在运行（默认: http://localhost:8000）")
    logger.info("2. 请在代码中填写有效的登录令牌:")
    logger.info("   修改 SessionSendAPITester 初始化中的 login_token 参数")
    logger.info("3. 如果需要，可以修改 base_url 指向正确的服务器地址")
    logger.info("4. 运行测试: python test_api_sessions_send.py")
    logger.info("")
    logger.info("测试内容:")
    logger.info("- 带 desktop_id 的消息发送")
    logger.info("- 带资源的消息发送") 
    logger.info("- 会话延续测试")
    logger.info("- 错误情况测试")
    logger.info("")


async def main():
    """主函数"""
    print_usage_instructions()
    
    logger.info("开始 /api/sessions/send 接口测试...")
    
    # 检查登录令牌
    tester = SessionSendAPITester()
    if tester.login_token == "YOUR_LOGIN_TOKEN_HERE":
        logger.warning("⚠️ 请先设置有效的登录令牌！")
        logger.info("请修改代码中的 login_token 参数，然后重新运行测试")
        return
    
    success_count = 0
    total_tests = 4
    
    # 测试带 desktop_id 的消息发送
    logger.info("\n" + "="*60)
    desktop_results = await test_send_message_with_desktop_id()
    if desktop_results:
        success_count += 1
        logger.success("✅ 桌面ID测试完成")
    else:
        logger.error("❌ 桌面ID测试失败")
    
    # 测试带资源的消息发送
    logger.info("\n" + "="*60)
    resource_result = await test_send_message_with_resources()
    if resource_result:
        success_count += 1
        logger.success("✅ 资源测试完成")
    else:
        logger.error("❌ 资源测试失败")
    
    # 测试会话延续
    logger.info("\n" + "="*60)
    session_result = await test_send_message_session_continuation()
    if session_result:
        success_count += 1
        logger.success("✅ 会话延续测试完成")
    else:
        logger.error("❌ 会话延续测试失败")
    
    # 测试错误情况
    logger.info("\n" + "="*60)
    error_result = await test_error_cases()
    if error_result:
        success_count += 1
        logger.success("✅ 错误情况测试完成")
    else:
        logger.error("❌ 错误情况测试失败")
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有 /api/sessions/send 接口测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
