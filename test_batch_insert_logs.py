#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 batch_insert_logs 批量插入日志功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from domain.models.enums import KbOperationType, KbTargetType, KbState
from infrastructure.database.repositories.kb_operation_logs_repository import kb_operation_logs_repository


def test_batch_insert_logs():
    """测试 batch_insert_logs 方法"""
    print("开始测试 batch_insert_logs 方法...")
    
    try:
        # 1. 准备测试数据
        print("1. 准备测试数据...")
        logs_data = [
            {
                "kb_id": "test_kb_001",
                "wy_id": "test_wy_001",
                "ali_uid": 123456,
                "operation_type": KbOperationType.CREATE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": "session_001",
                "status": KbState.SUCCESS.value,
            },
            {
                "kb_id": "test_kb_001",
                "wy_id": "test_wy_001",
                "ali_uid": 123456,
                "operation_type": KbOperationType.DELETE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": "session_002",
                "status": KbState.SUCCESS.value,
            },
            {
                "kb_id": "test_kb_002",
                "wy_id": "test_wy_002",
                "ali_uid": 789012,
                "operation_type": KbOperationType.UPDATE.value,
                "target_type": KbTargetType.DOCUMENT.value,
                "target_id": "doc_001",
                "status": KbState.PROCESSING.value,
            },
        ]
        print(f"   准备了 {len(logs_data)} 条日志数据")
        
        # 2. 批量插入日志
        print("2. 批量插入日志...")
        inserted_logs = kb_operation_logs_repository.batch_insert_logs(logs_data)
        print(f"   成功插入 {len(inserted_logs)} 条日志")
        
        # 3. 验证插入结果
        print("3. 验证插入结果...")
        for i, log in enumerate(inserted_logs):
            print(f"   日志 {i+1}: ID={log['id']}, kb_id={log['kb_id']}, target_id={log['target_id']}")
        
        # 4. 查询验证
        print("4. 查询验证...")
        # 查询第一条日志
        logs = kb_operation_logs_repository.list_logs(
            kb_id="test_kb_001",
            target_id="session_001"
        )
        print(f"   查询到 {len(logs)} 条匹配的日志")
        
        # 5. 测试空列表
        print("5. 测试空列表...")
        empty_logs = kb_operation_logs_repository.batch_insert_logs([])
        print(f"   空列表插入结果: {len(empty_logs)} 条")
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_batch_insert_logs_performance():
    """测试批量插入性能"""
    print("\n开始测试批量插入性能...")
    
    try:
        # 1. 准备大量测试数据
        print("1. 准备大量测试数据...")
        logs_data = []
        for i in range(100):
            logs_data.append({
                "kb_id": f"perf_kb_{i % 10}",
                "wy_id": f"perf_wy_{i % 5}",
                "ali_uid": 100000 + i,
                "operation_type": KbOperationType.CREATE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": f"perf_session_{i}",
                "status": KbState.SUCCESS.value,
            })
        print(f"   准备了 {len(logs_data)} 条日志数据")
        
        # 2. 批量插入
        print("2. 批量插入...")
        import time
        start_time = time.time()
        
        inserted_logs = kb_operation_logs_repository.batch_insert_logs(logs_data)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   成功插入 {len(inserted_logs)} 条日志")
        print(f"   耗时: {duration:.3f} 秒")
        print(f"   平均每条日志: {duration/len(inserted_logs)*1000:.2f} 毫秒")
        
        print("性能测试完成！")
        
    except Exception as e:
        print(f"性能测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_batch_insert_logs_error_handling():
    """测试错误处理"""
    print("\n开始测试错误处理...")
    
    try:
        # 1. 测试缺少必需字段
        print("1. 测试缺少必需字段...")
        invalid_logs_data = [
            {
                "kb_id": "test_kb_001",
                "wy_id": "test_wy_001",
                # 缺少 ali_uid
                "operation_type": KbOperationType.CREATE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": "session_001",
            }
        ]
        
        try:
            kb_operation_logs_repository.batch_insert_logs(invalid_logs_data)
            print("   错误：应该抛出异常但没有")
        except Exception as e:
            print(f"   正确捕获异常: {e}")
        
        # 2. 测试无效的数据类型
        print("2. 测试无效的数据类型...")
        invalid_type_logs_data = [
            {
                "kb_id": "test_kb_001",
                "wy_id": "test_wy_001",
                "ali_uid": "invalid_uid",  # 应该是整数
                "operation_type": KbOperationType.CREATE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": "session_001",
            }
        ]
        
        try:
            kb_operation_logs_repository.batch_insert_logs(invalid_type_logs_data)
            print("   错误：应该抛出异常但没有")
        except Exception as e:
            print(f"   正确捕获异常: {e}")
        
        print("错误处理测试完成！")
        
    except Exception as e:
        print(f"错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_batch_insert_logs()
    test_batch_insert_logs_performance()
    test_batch_insert_logs_error_handling() 