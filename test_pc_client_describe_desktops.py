#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 PC 客户端的 describe_desktops 方法
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_describe_desktops():
    """测试 describe_desktops 方法"""
    print("=== 测试 PC 客户端 describe_desktops 方法 ===")
    
    # 设置测试环境
    os.environ["ENV_FOR_DYNACONF"] = "daily"
    
    # 测试用的 end_user_id
    test_end_user_id = "57034d6f-7edb-4817-bb3d-5af71772f76f"
    
    try:
        # 1. 先初始化全局无AK认证（如果需要）
        print("1. 初始化全局无AK认证...")
        try:
            from src.shared.startup import initialize_app
            init_result = initialize_app()
            print(f"   初始化结果: {init_result}")
        except Exception as e:
            print(f"   ⚠️ 全局初始化失败: {e}")
            print("   继续使用原有的客户端初始化方式...")
        
        # 2. 创建 PC 客户端
        print("2. 创建 PC 客户端...")
        from src.popclients.pc_inside_client import PcInsideClient, get_pc_inside_client
        
        try:
            # 尝试使用便捷函数创建客户端
            pc_client = get_pc_inside_client()
            print("   ✅ PC 客户端创建成功")
            print(f"   客户端信息: {pc_client.get_client_info()}")
        except Exception as e:
            print(f"   ❌ PC 客户端创建失败: {e}")
            return
        
        # 3. 测试 describe_desktops 方法
        print(f"3. 测试 describe_desktops 方法，end_user_id: {test_end_user_id}")
        try:
            response = pc_client.describe_desktops(
                end_user_id=test_end_user_id
            )
            
            print("   ✅ describe_desktops 调用成功")
            print(f"   响应类型: {type(response)}")
            
            # 尝试解析响应内容
            if hasattr(response, 'body'):
                print(f"   响应体: {response.body}")
                if hasattr(response.body, 'desktops'):
                    desktops = response.body.desktops
                    print(f"   桌面数量: {len(desktops) if desktops else 0}")
                    if desktops:
                        for i, desktop in enumerate(desktops[:3]):  # 只显示前3个
                            print(f"   桌面 {i+1}: {desktop}")
            else:
                print(f"   响应内容: {response}")
                
        except Exception as e:
            print(f"   ❌ describe_desktops 调用失败: {e}")
            print(f"   错误类型: {type(e).__name__}")
            
            # 打印详细错误信息
            import traceback
            print("   详细错误信息:")
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_with_different_params():
    """测试不同参数的 describe_desktops 调用"""
    print("\n=== 测试不同参数的 describe_desktops 调用 ===")
    
    test_cases = [
        {"end_user_id": "57034d6f-7edb-4817-bb3d-5af71772f76f", "desc": "指定的测试用户ID"},
        {"end_user_id": None, "desc": "空用户ID（获取所有桌面）"},
    ]
    
    try:
        from src.popclients.pc_inside_client import get_pc_inside_client
        pc_client = get_pc_inside_client()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"{i}. 测试 {test_case['desc']}")
            try:
                if test_case['end_user_id']:
                    response = pc_client.describe_desktops(
                        end_user_id=test_case['end_user_id']
                    )
                else:
                    response = pc_client.describe_desktops()
                
                print(f"   ✅ 调用成功，响应类型: {type(response)}")
                
            except Exception as e:
                print(f"   ❌ 调用失败: {e}")
                
    except Exception as e:
        print(f"❌ 无法创建客户端: {e}")

def main():
    """主测试函数"""
    print("开始测试 PC 客户端 describe_desktops 方法...")
    
    test_describe_desktops()
    test_with_different_params()
    
    print("\n=== 测试完成 ===")
    print("注意：")
    print("1. 如果在本地环境运行，无AK认证可能会失败")
    print("2. 实际的API调用需要有效的网络连接和权限")
    print("3. 响应内容取决于实际的桌面配置")

if __name__ == "__main__":
    main()
