#!/usr/bin/env python3
"""
AppStream内部客户端测试脚本
测试AppStreamInnerClient的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.popclients.appstream_inner_client import (
    AppStreamInnerClient, 
    AppStreamInnerClientError,
    get_appstream_inner_client,
    reset_appstream_inner_client
)


def test_client_initialization():
    """测试客户端初始化"""
    logger.info("=== 测试AppStream内部客户端初始化 ===")
    
    try:
        # 测试正常初始化
        client = AppStreamInnerClient(
            access_key_id="test_access_key_id",
            access_key_secret="test_access_key_secret",
            endpoint="test-endpoint.aliyuncs.com"
        )
        
        logger.success("✅ 客户端初始化成功")
        logger.info(f"客户端信息: {client}")
        
        # 测试获取客户端信息
        client_info = client.get_client_info()
        logger.info(f"客户端配置信息: {client_info}")
        
        # 验证配置信息
        assert client_info["access_key_id"] == "test_access_key_id"
        assert client_info["endpoint"] == "test-endpoint.aliyuncs.com"
        assert client_info["connect_timeout"] == 5000
        assert client_info["read_timeout"] == 10000
        
        logger.success("✅ 客户端配置信息验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 客户端初始化测试失败: {e}")
        return False


def test_client_validation():
    """测试客户端参数验证"""
    logger.info("=== 测试客户端参数验证 ===")
    
    try:
        # 测试缺少access_key_id
        try:
            AppStreamInnerClient(
                access_key_id="",
                access_key_secret="test_secret"
            )
            logger.error("❌ 应该抛出access_key_id为空的异常")
            return False
        except AppStreamInnerClientError as e:
            logger.success(f"✅ 正确捕获access_key_id为空异常: {e}")
        
        # 测试缺少access_key_secret
        try:
            AppStreamInnerClient(
                access_key_id="test_key",
                access_key_secret=""
            )
            logger.error("❌ 应该抛出access_key_secret为空的异常")
            return False
        except AppStreamInnerClientError as e:
            logger.success(f"✅ 正确捕获access_key_secret为空异常: {e}")
        
        # 测试默认端点
        client = AppStreamInnerClient(
            access_key_id="test_key",
            access_key_secret="test_secret"
        )
        
        client_info = client.get_client_info()
        assert client_info["endpoint"] == "appstream-center-inner.aliyuncs.com"
        logger.success("✅ 默认端点设置正确")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 客户端参数验证测试失败: {e}")
        return False


def test_singleton_pattern():
    """测试单例模式"""
    logger.info("=== 测试单例模式 ===")
    
    try:
        # 重置单例
        reset_appstream_inner_client()
        
        # 测试首次调用需要参数
        try:
            get_appstream_inner_client()
            logger.error("❌ 应该抛出缺少参数的异常")
            return False
        except AppStreamInnerClientError as e:
            logger.success(f"✅ 正确捕获缺少参数异常: {e}")
        
        # 创建单例实例
        client1 = get_appstream_inner_client(
            access_key_id="test_key",
            access_key_secret="test_secret",
            endpoint="test-endpoint.com"
        )
        
        # 再次获取应该返回同一个实例
        client2 = get_appstream_inner_client()
        
        assert client1 is client2
        logger.success("✅ 单例模式工作正常")
        
        # 测试重置单例
        reset_appstream_inner_client()
        client3 = get_appstream_inner_client(
            access_key_id="new_key",
            access_key_secret="new_secret"
        )
        
        assert client3 is not client1
        logger.success("✅ 单例重置功能正常")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单例模式测试失败: {e}")
        return False


def test_get_sts_token_method():
    """测试GetStsToken方法结构"""
    logger.info("=== 测试GetStsToken方法结构 ===")
    
    try:
        client = AppStreamInnerClient(
            access_key_id="test_key",
            access_key_secret="test_secret"
        )
        
        # 检查方法是否存在
        assert hasattr(client, 'get_sts_token')
        assert hasattr(client, 'get_sts_token_async')
        
        logger.success("✅ GetStsToken方法存在")
        
        # 检查方法签名（通过调用help或inspect）
        import inspect
        
        # 检查同步方法签名
        sig = inspect.signature(client.get_sts_token)
        params = list(sig.parameters.keys())
        expected_params = [
            'account_type', 'auto_create_user', 'end_user_id', 
            'external_user_id', 'policy', 'user_ali_uid'
        ]
        
        for param in expected_params:
            assert param in params, f"缺少参数: {param}"
        
        logger.success("✅ GetStsToken方法签名正确")
        
        # 检查异步方法签名
        async_sig = inspect.signature(client.get_sts_token_async)
        async_params = list(async_sig.parameters.keys())
        
        for param in expected_params:
            assert param in async_params, f"异步方法缺少参数: {param}"
        
        logger.success("✅ GetStsToken异步方法签名正确")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GetStsToken方法结构测试失败: {e}")
        return False


def test_import_structure():
    """测试导入结构"""
    logger.info("=== 测试导入结构 ===")
    
    try:
        # 测试模型导入
        from src.popclients.appstream_inner_client import appstream_models
        
        # 检查关键模型是否存在
        assert hasattr(appstream_models, 'GetStsTokenRequest')
        assert hasattr(appstream_models, 'GetStsTokenResponse')
        assert hasattr(appstream_models, 'GetStsTokenResponseBody')
        
        logger.success("✅ AppStream模型导入正确")
        
        # 测试客户端导入
        from src.popclients.appstream_inner_client import client
        assert hasattr(client, 'Client')
        
        logger.success("✅ AppStream客户端导入正确")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 导入结构测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始AppStream内部客户端测试...")
    
    success_count = 0
    total_tests = 5
    
    # 测试客户端初始化
    if test_client_initialization():
        success_count += 1
        logger.success("✅ 客户端初始化测试通过")
    else:
        logger.error("❌ 客户端初始化测试失败")
    
    # 测试参数验证
    if test_client_validation():
        success_count += 1
        logger.success("✅ 客户端参数验证测试通过")
    else:
        logger.error("❌ 客户端参数验证测试失败")
    
    # 测试单例模式
    if test_singleton_pattern():
        success_count += 1
        logger.success("✅ 单例模式测试通过")
    else:
        logger.error("❌ 单例模式测试失败")
    
    # 测试GetStsToken方法
    if test_get_sts_token_method():
        success_count += 1
        logger.success("✅ GetStsToken方法测试通过")
    else:
        logger.error("❌ GetStsToken方法测试失败")
    
    # 测试导入结构
    if test_import_structure():
        success_count += 1
        logger.success("✅ 导入结构测试通过")
    else:
        logger.error("❌ 导入结构测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有AppStream内部客户端测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
