#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话业务服务
"""
from typing import Optional, List, Dict, Any, Tuple, AsyncGenerator
from loguru import logger
import uuid
import asyncio
import json
from datetime import datetime

from ...infrastructure.database.repositories.session_repository import session_db_service
from ...infrastructure.database.models.session_models import SessionModel
from .auth_service import AuthContext
from .file_service import file_service
from ...popclients.waiy_infra_client import create_waiy_infra_client, WaiyInfraClientError
from ...application.api_models import ResourceType, SessionResource
from .auth_service import auth_service
from .knowledge_service import knowledgebase_service
from ...infrastructure.database.models.auth_models import PermissionType
from ...popclients.appstream_inner_client import get_appstream_inner_client, AppStreamInnerClientError
from ...infrastructure.redis import RedisClient


class SessionPermissionError(Exception):
    """会话权限异常"""
    def __init__(self, message: str, session_id: str = None):
        self.message = message
        self.session_id = session_id
        super().__init__(message)
    
    def __str__(self):
        if self.session_id:
            return f"会话权限错误 ({self.session_id}): {self.message}"
        return f"会话权限错误: {self.message}"
from src.infrastructure.memory.memory_sdk import memory_sdk
from .message_processor import MessageProcessor
from .sse_manager import SSEManager


class SSEStreamManager:
    """SSE流管理器 - 简化版本，直接基于Memory SDK事件"""
    
    def __init__(self):
        self.sse_connections: Dict[str, asyncio.Queue] = {}
        self.heartbeat_tasks: Dict[str, asyncio.Task] = {}
    
    def create_connection(self, session_id: str) -> asyncio.Queue:
        """为session创建消息队列"""
        message_queue = asyncio.Queue()
        self.sse_connections[session_id] = message_queue
        return message_queue
    
    def close_connection(self, session_id: str):
        """关闭session连接"""
        if session_id in self.sse_connections:
            del self.sse_connections[session_id]
        
        if session_id in self.heartbeat_tasks:
            task = self.heartbeat_tasks[session_id]
            if not task.done():
                task.cancel()
            del self.heartbeat_tasks[session_id]
    
    def close_session_connection(self, session_id: str):
        """关闭session连接 - 兼容MessageProcessor接口"""
        self.close_connection(session_id)
    
    async def push_event(self, session_id: str, event_data: Dict[str, Any]):
        """推送事件到SSE连接"""
        if session_id in self.sse_connections:
            try:
                await self.sse_connections[session_id].put(event_data)
                logger.debug(f"[SSEStreamManager] 事件推送成功: session_id={session_id}")
            except Exception as e:
                logger.error(f"[SSEStreamManager] 事件推送失败: session_id={session_id}, error={e}")
    
    async def push_to_sse(self, session_id: str, round_id: str, data: Dict[str, Any], event_type: str = "message"):
        """推送数据到SSE连接 - 兼容MessageProcessor接口"""
        event_data = {
            "eventType": event_type,
            "data": data
        }
        await self.push_event(session_id, event_data)
    
    async def heartbeat_loop(self, session_id: str, message_queue: asyncio.Queue):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(3)  # 每3秒发送一次心跳
                if session_id not in self.sse_connections:
                    break
                
                heartbeat_data = {
                    "type": "heartbeat",
                    "data": {"timestamp": datetime.now().isoformat()}
                }
                await message_queue.put(heartbeat_data)
        except asyncio.CancelledError:
            logger.info(f"[SSEStreamManager] 心跳任务被取消: session_id={session_id}")
        except Exception as e:
            logger.error(f"[SSEStreamManager] 心跳异常: session_id={session_id}, error={e}")


class SessionListParams:
    """会话列表查询参数"""
    
    def __init__(
        self,
        page_size: int = 20,
        next_token: Optional[str] = None,
        order_by: str = "gmt_modified",
        order_direction: str = "desc",
        search_keyword: Optional[str] = None,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        self.page_size = min(max(1, page_size), 100)  # 限制页面大小在1-100之间
        self.next_token = next_token
        self.order_by = order_by if order_by in ["gmt_create", "gmt_modified"] else "gmt_modified"
        self.order_direction = order_direction if order_direction in ["asc", "desc"] else "desc"
        self.search_keyword = search_keyword
        self.agent_id = agent_id
        self.session_id = session_id
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size


class SessionInfo:
    """会话信息模型"""
    
    def __init__(self, session_model):
        self.session_id = session_model.session_id
        self.title = session_model.title or "无标题"
        self.agent_id = session_model.agent_id
        self.ali_uid = session_model.ali_uid
        self.wy_id = session_model.wy_id
        self.status = self._format_status(session_model.status)
        self.gmt_create = session_model.gmt_create
        self.gmt_modified = session_model.gmt_modified
        self.metadata = session_model.meta_data or {}

        # 从metadata中提取额外信息
        self.total_rounds = self._extract_total_rounds()
        self.last_user_prompt = self._extract_last_user_prompt()
    
    def _format_status(self, status) -> str:
        """格式化状态"""
        # 现在状态直接是字符串
        return str(status).lower() if status else 'unknown'
    
    def _extract_total_rounds(self) -> int:
        """从metadata中提取总轮次数"""
        return self.metadata.get("total_rounds", 0)
    
    def _extract_last_user_prompt(self) -> str:
        """从metadata中提取最后一次用户输入"""
        return self.metadata.get("last_user_prompt", "")[:100]  # 限制长度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "sessionId": self.session_id,
            "title": self.title,
            "agentId": self.agent_id,
            "aliUid": self.ali_uid,
            "wyId": self.wy_id,
            "status": self.status,
            "gmtCreate": self.gmt_create.isoformat() if self.gmt_create else None,
            "gmtModified": self.gmt_modified.isoformat() if self.gmt_modified else None,
            "totalRounds": self.total_rounds,
            "lastUserPrompt": self.last_user_prompt,
            "metadata": self.metadata
        }


class SessionListResult:
    """会话列表结果"""
    
    def __init__(
        self,
        sessions: List[SessionInfo],
        page_size: int,
        total_count: int,
        next_token: Optional[str] = None,
        has_more: bool = False
    ):
        self.sessions = sessions
        self.page_size = page_size
        self.total_count = total_count
        self.next_token = next_token
        self.has_more = has_more
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "sessions": [session.to_dict() for session in self.sessions],
            "page_size": self.page_size,
            "total_count": self.total_count,
            "has_more": self.has_more
        }
        if self.next_token:
            result["next_token"] = self.next_token
        return result


class SessionService:
    """会话业务服务"""
    
    def __init__(self):
        self.session_db_service = session_db_service
        self.memory_sdk = memory_sdk  # 使用全局MemorySDK实例
        self.sse_stream_manager = SSEManager()
        self.message_processor = MessageProcessor()
        # 设置 MessageProcessor 与 SSE 流管理器的连接
        self.message_processor.set_sse_manager(self.sse_stream_manager)
        # 设置 SSEManager 的 session_loader
        self.sse_stream_manager.set_session_loader(self)
        self._initialized = False
        self._cleanup_task = None
    
    async def initialize(self):
        """初始化会话服务"""
        if self._initialized:
            logger.warning("[SessionService] 服务已经初始化，跳过重复初始化")
            return
            
        try:
            logger.info("[SessionService] 开始初始化会话服务...")

            # 1. 注册Memory SDK回调处理新消息事件（MemorySDK已经是全局单例）
            self.memory_sdk.register_callback("on_new_message", self.message_processor.handle_new_message)
            logger.info("[SessionService] Memory SDK回调注册完成")

            # 2. 启动后台清理任务
            self._cleanup_task = asyncio.create_task(self._background_cleanup_task())
            logger.info("[SessionService] 后台清理任务启动完成")

            self._initialized = True
            logger.info("[SessionService] 会话服务初始化完成")
            
        except Exception as e:
            logger.error(f"[SessionService] 初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭会话服务，清理资源"""
        try:
            logger.info("[SessionService] 开始关闭会话服务...")
            
            # 1. 取消后台清理任务
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
                logger.info("[SessionService] 后台清理任务已停止")
            
            # 2. 清理所有SSE连接
            active_sessions = list(self.sse_stream_manager.sse_connections.keys())
            for session_id in active_sessions:
                self.sse_stream_manager.close_session_connection(session_id)
            logger.info(f"[SessionService] 已清理 {len(active_sessions)} 个SSE连接")

            self._initialized = False
            logger.info("[SessionService] 会话服务关闭完成")
            
        except Exception as e:
            logger.error(f"[SessionService] 关闭会话服务异常: {e}")
    
    async def _handle_memory_message(self, event):
        """处理Memory SDK新消息回调"""
        try:
            logger.debug(f"[SessionService] 收到Memory消息: event_type={getattr(event, 'type', 'unknown')}")
            
            session_id = getattr(event, 'session_id', None)
            if session_id and session_id in self.sse_stream_manager.sse_connections:
                # 处理事件并推送到对应的SSE连接
                await self._process_memory_event(session_id, event)
            
        except Exception as e:
            logger.error(f"[SessionService] 处理Memory消息异常: {e}")
    
    async def _handle_memory_round_failed(self, event):
        """处理Memory SDK轮次失败回调"""
        try:
            logger.error(f"[SessionService] Memory轮次失败: {event}")
            
            session_id = getattr(event, 'session_id', None)
            if session_id and session_id in self.sse_stream_manager.sse_connections:
                # 发送错误事件
                error_event = {
                    "type": "error",
                    "data": {
                        "eventType": "error",
                        "data": {"error": f"轮次处理失败: {str(event)}"}
                    }
                }
                await self.sse_stream_manager.push_to_sse(session_id, "", error_event, "error")
            
        except Exception as e:
            logger.error(f"[SessionService] 处理Memory轮次失败异常: {e}")
    
    async def _background_cleanup_task(self):
        """后台清理任务"""
        try:
            while True:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                # 清理超时的SSE连接（可选实现）
                current_time = datetime.now()
                cleanup_count = 0
                
                # 这里可以添加具体的清理逻辑，比如：
                # - 清理长时间无活动的SSE连接
                # - 清理过期的会话数据
                # - 清理内存中的缓存等
                
                if cleanup_count > 0:
                    logger.info(f"[SessionService] 后台清理完成: 清理了 {cleanup_count} 个资源")
                    
        except asyncio.CancelledError:
            logger.info("[SessionService] 后台清理任务被取消")
        except Exception as e:
            logger.error(f"[SessionService] 后台清理任务异常: {e}")
    
    def get_user_sessions(
        self,
        context: AuthContext,
        params: SessionListParams
    ) -> SessionListResult:
        """
        获取用户会话列表
        
        Args:
            context: 认证上下文
            params: 查询参数
            
        Returns:
            SessionListResult: 会话列表结果
        """
        try:
            logger.info(f"[SessionService] 获取用户会话列表: user={context.user_key}, page_size={params.page_size}, next_token={params.next_token}")
            
            # 查询会话列表（获取limit+1条用于判断是否还有更多数据）
            # 默认排除状态为DELETE的会话
            session_models = self.session_db_service.list_sessions(
                limit=params.limit + 1,
                ali_uid=str(context.ali_uid),
                agent_id=params.agent_id,
                next_token=params.next_token,
                search_keyword=params.search_keyword,
                status_filter="!DELETED"  # 排除已删除的会话
            )
            
            # 处理分页逻辑
            has_more = len(session_models) > params.limit
            if has_more:
                session_models = session_models[:-1]  # 移除多余的一条
            
            sessions = [SessionInfo(model) for model in session_models]
            
            # 获取总数（排除已删除的会话）
            total_count = self.session_db_service.count_sessions(
                ali_uid=str(context.ali_uid),
                agent_id=params.agent_id,
                search_keyword=params.search_keyword,
                status_filter="!DELETE"  # 排除已删除的会话
            )
            
            # 生成next_token
            next_token = None
            if has_more and sessions:
                from src.domain.utils.next_token_utils import NextTokenUtils
                last_session = session_models[-1]
                next_token = NextTokenUtils.encode(
                    id=last_session.id,
                    date=last_session.gmt_modified
                )
            
            logger.info(f"[SessionService] 查询完成: user={context.user_key}, 返回 {len(sessions)} 个会话, 总数 {total_count}, has_more={has_more}")
            
            return SessionListResult(
                sessions=sessions,
                page_size=params.page_size,
                total_count=total_count,
                next_token=next_token,
                has_more=has_more
            )
            
        except Exception as e:
            logger.error(f"[SessionService] 获取用户会话列表失败: user={context.user_key}, error={e}")
            raise
    
    def get_session_with_permission_check(
        self, 
        context: AuthContext, 
        session_id: str, 
        required_permission: PermissionType,
    ) -> SessionModel:
        """
        获取会话数据，带权限校验
        
        Args:
            context: 认证上下文
            session_id: 会话ID
            required_permission: 所需权限，默认为read
            
        Returns:
            SessionModel: 会话数据库模型
            
        Raises:
            SessionPermissionError: 权限不足时抛出
            ValueError: 会话不存在时抛出
        """
        try:
            logger.info(f"[SessionService] 校验会话权限: user={context.user_key}, session_id={session_id}, permission={required_permission}")
            
            # 1. 先检查权限
            auth_result = auth_service.check_resource_permission(
                context=context,
                resource_type=ResourceType.SESSION,
                resource_id=session_id,
                required_permission=required_permission
            )
            
            if not auth_result.success:
                logger.warning(f"[SessionService] 会话权限校验失败: user={context.user_key}, session_id={session_id}, reason={auth_result.message}")
                raise SessionPermissionError(auth_result.message, session_id)
            
            # 2. 权限检查通过后，获取会话数据
            session_model = self.session_db_service.get_session_by_id(session_id)
            
            if not session_model:
                logger.warning(f"[SessionService] 会话不存在: session_id={session_id}")
                raise ValueError(f"会话不存在: {session_id}")
            
            logger.info(f"[SessionService] 会话权限校验成功: user={context.user_key}, session_id={session_id}, permissions={auth_result.permissions}")
            return session_model
            
        except SessionPermissionError:
            raise
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"[SessionService] 会话权限校验异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise SessionPermissionError(f"权限校验异常: {str(e)}", session_id)

    def get_session_detail(self, context: AuthContext, session_id: str) -> Optional[SessionInfo]:
        """
        获取会话详情

        Args:
            context: 认证上下文
            session_id: 会话ID

        Returns:
            SessionInfo: 会话信息，如果不存在返回None
        """
        try:
            logger.info(f"[SessionService] 获取会话详情: user={context.user_key}, session_id={session_id}")

            # 使用带权限校验的方法获取会话
            try:
                session_model = self.get_session_with_permission_check(context, session_id, PermissionType.READ)
                return SessionInfo(session_model)
            except (SessionPermissionError, ValueError):
                # 权限不足或会话不存在，返回None
                logger.warning(f"[SessionService] 无法获取会话: user={context.user_key}, session_id={session_id}")
                return None

        except Exception as e:
            logger.error(f"[SessionService] 获取会话详情失败: user={context.user_key}, session_id={session_id}, error={e}")
            raise

    def get_sessions_by_ids(self, session_ids: List[str]) -> List[SessionInfo]:
        """
        根据会话ID列表批量获取会话信息（不进行权限检查）

        Args:
            session_ids: 会话ID列表

        Returns:
            List[SessionInfo]: 会话信息列表
        """
        try:
            if not session_ids:
                logger.debug("[SessionService] 批量获取会话: 空列表")
                return []

            logger.info(f"[SessionService] 批量获取会话: 查询{len(session_ids)}个会话")

            # 从数据库批量查询会话
            session_models = self.session_db_service.get_sessions_by_ids(session_ids)

            # 转换为SessionInfo对象
            sessions = [SessionInfo(session_model) for session_model in session_models]

            logger.info(f"[SessionService] 批量获取会话完成: 查询{len(session_ids)}个, 返回{len(sessions)}个")
            return sessions

        except Exception as e:
            logger.error(f"[SessionService] 批量获取会话失败: session_ids={session_ids}, error={e}")
            raise

    def rename_session(self, context: AuthContext, session_id: str, new_title: str) -> bool:
        """
        重命名会话

        Args:
            context: 认证上下文
            session_id: 会话ID
            new_title: 新标题

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"[SessionService] 重命名会话: user={context.user_key}, session_id={session_id}, new_title={new_title}")

            if not new_title or not new_title.strip():
                raise ValueError("新标题不能为空")

            new_title = new_title.strip()
            if len(new_title) > 200:  # 限制标题长度
                raise ValueError("标题长度不能超过200个字符")

            # 使用带权限校验的方法获取会话（需要write权限）
            session_model = self.get_session_with_permission_check(context, session_id, PermissionType.WRITE)

            # 执行重命名
            success = self.session_db_service.update_session_title(session_id, new_title)

            if success:
                logger.info(f"[SessionService] 会话重命名成功: user={context.user_key}, session_id={session_id}, old_title={session_model.title}, new_title={new_title}")
            else:
                logger.error(f"[SessionService] 会话重命名失败: user={context.user_key}, session_id={session_id}")

            return success

        except (SessionPermissionError, ValueError):
            raise
        except Exception as e:
            logger.error(f"[SessionService] 重命名会话异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise

    def delete_session(self, context: AuthContext, session_id: str) -> bool:
        """
        删除会话

        Args:
            context: 认证上下文
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"[SessionService] 删除会话: user={context.user_key}, session_id={session_id}")

            # 使用带权限校验的方法获取会话（需要delete权限）
            session_model = self.get_session_with_permission_check(context, session_id, PermissionType.DELETE)

            # 从鉴权系统中注销资源
            try:
                from .auth_service import auth_service, ResourceType
                auth_service.unregister_resource(
                    context=context,
                    resource_type=ResourceType.SESSION,
                    resource_id=session_id
                )
                logger.info(f"[SessionService] 会话鉴权注销成功: session_id={session_id}")
            except Exception as auth_error:
                logger.error(f"[SessionService] 会话鉴权注销失败: session_id={session_id}, error={auth_error}")
                # 鉴权注销失败不阻止会话删除流程

            # 执行删除（软删除）
            success = self.session_db_service.delete_session(session_id)

            if success:
                logger.info(f"[SessionService] 会话删除成功: user={context.user_key}, session_id={session_id}, title={session_model.title}")
            else:
                logger.error(f"[SessionService] 会话删除失败: user={context.user_key}, session_id={session_id}")

            return success

        except (SessionPermissionError, ValueError):
            raise
        except Exception as e:
            logger.error(f"[SessionService] 删除会话异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise

    async def send_message(
        self,
        session_id: Optional[str],
        prompt: str,
        agent_id: str,
        desktop_id: Optional[str],
        auth_code: Optional[str],
        resources: Optional[List[SessionResource]],
        context: AuthContext
    ) -> Tuple[str, str]:
        """
        发送消息到Agent

        Args:
            session_id: 会话ID，为空则创建新会话
            prompt: 用户请求内容
            agent_id: Agent ID
            desktop_id: 桌面ID
            auth_code: token
            resources: 对话资源列表
            context: 认证上下文

        Returns:
            Tuple[str, str]: (session_id, round_id)
        """
        try:
            logger.info(f"[SessionService] 发送消息: user={context.user_key}, agent_id={agent_id}, session_id={session_id}")

            # 从数据库模型重建Session领域对象
            session = self.get_or_create_session_domain(session_id, context.ali_uid, agent_id, context.wy_id)
            
            logger.info(f"[SessionService] 使用会话: session_id={session.session_id}")
            
            # 异步标题生成
            if not session.title or session.title.strip() == "":
                asyncio.create_task(self._generate_session_title_async(session.session_id, prompt))

            # 处理资源
            waiy_resources = []
            if resources:
                waiy_resources = await self._process_resources(resources, context)

            # 创建WaiyInfra客户端
            waiy_client = create_waiy_infra_client()

            # 构建运行时资源配置
            runtime_resource = self._build_runtime_resource(desktop_id, auth_code, context)

            # 创建消息上下文
            message_context = waiy_client.create_async_message_context(
                runtime_resource=runtime_resource,
                session_id=session.session_id,
                user_id=str(context.ali_uid)
            )

            logger.info(f"[SessionService] 发送消息到waiy-infra: session_id={session.session_id}, prompt={prompt[:50]}..., desktop_id={desktop_id}, auth_code={'***' if auth_code else 'None'}")

            # 发送消息到WaiyInfra（使用message_async方法）
            response = waiy_client.message_async_sync(
                session_id=session_id,
                app_id=agent_id,
                message=prompt,
                context=message_context,
                resources=waiy_resources
            )

            # 从响应中提取round_id（参考session_manager的逻辑）
            if not response.body:
                raise ValueError("waiy-infra未返回有效响应")

            # 检查响应格式并提取round_id
            round_id = None
            if hasattr(response.body, 'trace_id'):
                round_id = response.body.trace_id

            if not round_id:
                raise ValueError("无法获取有效的round_id")

            session.start_processing()

            logger.info(f"[SessionService] 消息发送成功: session_id={session.session_id}, round_id={round_id}, response = {response.body}")

            return session.session_id, round_id

        except WaiyInfraClientError as e:
            logger.exception(f"[SessionService] WaiyInfra客户端错误: {e}")
            raise Exception(f"发送消息失败: {e}")
        except Exception as e:
            logger.error(f"[SessionService] 发送消息异常: user={context.user_key}, error={e}")
            raise

    def get_or_create_session_domain(self, session_id: Optional[str], ali_uid: int, agent_id: str, wy_id: str = "") -> "Session":
        """
        获取或创建Session领域对象（参考session_manager的逻辑）

        Args:
            session_id: 会话ID，为空则创建新会话
            ali_uid: 阿里云用户ID
            agent_id: Agent ID
            wy_id: 无影ID

        Returns:
            Session: Session领域对象
        """
        try:
            if session_id:
                # 从数据库查询现有会话
                session_model = session_db_service.get_session_by_id(session_id)
                if session_model:
                    logger.info(f"[SessionService] 从数据库加载会话: {session_id}")
                    # 从数据库模型重建Session领域对象
                    session = self._rebuild_session_from_model(session_model)
                    return session
                else:
                    # Session不存在
                    raise ValueError(f"Session不存在: {session_id}")

            # 创建新会话
            new_session_id = f"sess_{uuid.uuid4().hex}"
            session_model = session_db_service.create_session(
                ali_uid=ali_uid,
                agent_id=agent_id,
                session_id=new_session_id,
                metadata={},
                wy_id=wy_id
            )


            logger.info(f"[SessionService] 创建新会话: {new_session_id}")
            
            # 注册会话资源到鉴权系统
            try:
                from .auth_service import AuthContext
                auth_context = AuthContext(
                    ali_uid=ali_uid,
                    wy_id=wy_id
                )
                auth_service.register_resource(
                    context=auth_context,
                    resource_type=ResourceType.SESSION,
                    resource_id=new_session_id,
                    resource_name=f"会话 {new_session_id[:8]}",
                    is_public=False
                )
                logger.info(f"[SessionService] 会话资源注册成功: {new_session_id}")
            except Exception as e:
                logger.error(f"[SessionService] 会话资源注册失败: {new_session_id}, error={e}")
                # 不影响会话创建，继续执行
            
            # 从数据库模型重建Session领域对象
            session = self._rebuild_session_from_model(session_model)

            return session

        except Exception as e:
            logger.error(f"[SessionService] 获取或创建会话失败: {e}")
            raise

    def _rebuild_session_from_model(self, session_model: SessionModel) -> SessionModel:
        """
        从数据库模型重建Session领域对象

        Args:
            session_model: 数据库Session模型

        Returns:
            Session: Session领域对象
        """
        from ...domain.models.session import Session
        from ...domain.models.enums import SessionStatus
        
        # 转换状态字符串为枚举
        status_map = {
            'CREATE': SessionStatus.CREATE,
            'ACTIVE': SessionStatus.ACTIVE,
            'CLOSED': SessionStatus.CLOSED
        }
        status = status_map.get(session_model.status, SessionStatus.CREATE)
        
        # 创建Session领域对象
        session = Session(
            session_id=session_model.session_id,
            ali_uid=session_model.ali_uid,
            agent_id=session_model.agent_id,
            wy_id=getattr(session_model, 'wy_id', ''),
            title=session_model.title,
            status=status,
            gmt_create=session_model.gmt_create,
            gmt_modified=session_model.gmt_modified,
            metadata=session_model.meta_data or {}
        )
        
        # 设置事件监听，确保状态变更能同步到数据库
        self._setup_session_events(session)
        
        return session

    def _setup_session_events(self, session: "Session"):
        """
        设置Session事件监听

        Args:
            session: Session领域对象
        """
        session.on('status_changed', self._on_session_status_changed)

    def _on_session_status_changed(self, session: "Session", old_status, new_status, reason: str):
        """
        Session状态变更回调

        Args:
            session: Session领域对象
            old_status: 旧状态
            new_status: 新状态
            reason: 变更原因
        """
        try:
            # 将状态变更同步到数据库
            status_map = {
                'CREATE': 'CREATE',
                'ACTIVE': 'ACTIVE', 
                'CLOSED': 'CLOSED'
            }
            new_status_name = getattr(new_status, 'name', str(new_status))
            new_status_str = status_map.get(new_status_name, 'CREATE')
            session_db_service.update_session_status(session.session_id, new_status_str)

            old_status_name = getattr(old_status, 'name', str(old_status))
            logger.info(f"[SessionService] Session状态已同步到数据库: {session.session_id} {old_status_name} -> {new_status_name}")
            
        except Exception as e:
            logger.error(f"[SessionService] 同步Session状态到数据库失败: {e}")

    def _get_or_create_session(self, session_id: Optional[str], ali_uid: str, agent_id: str) -> SessionModel:
        """
        获取或创建会话（参考session_manager的逻辑）

        Args:
            session_id: 会话ID，为空则创建新会话
            ali_uid: 阿里云用户ID
            agent_id: Agent ID

        Returns:
            str: 会话ID
        """
        try:
            if session_id:
                # 从数据库查询现有会话
                session_model = session_db_service.get_session_by_id(session_id)
                if session_model:
                    logger.info(f"[SessionService] 从数据库加载会话: {session_id}")
                    return session_model
                else:
                    # Session不存在
                    raise ValueError(f"Session不存在: {session_id}")

            # 创建新会话
            new_session_id = f"sess_{uuid.uuid4().hex}"
            session_model = session_db_service.create_session(
                ali_uid=ali_uid,
                agent_id=agent_id,
                session_id=new_session_id,
                title=f"会话 {new_session_id[:8]}",
                metadata={},
                wy_id=""
            )

            logger.info(f"[SessionService] 创建新会话: {new_session_id}")
            return session_model

        except Exception as e:
            logger.error(f"[SessionService] 获取或创建会话失败: {e}")
            raise

    async def _process_resources(
        self,
        resources: List[SessionResource],
        context: AuthContext
    ) -> List:
        """
        处理会话资源，转换为WaiyInfra所需的格式

        Args:
            resources: 会话资源列表
            context: 认证上下文

        Returns:
            List: WaiyInfra资源列表
        """
        waiy_resources = []

        for resource in resources:
            try:
                if resource.type == ResourceType.FILE:
                    # 处理文件资源
                    file_resource = await self._process_file_resource(resource.resource_id, context)
                    if file_resource:
                        waiy_resources.append(file_resource)
                elif resource.type == ResourceType.KNOWLEDGE_BASE:
                    # 处理知识库资源
                    kb_resource = await self._process_knowledge_base_resource(resource.resource_id, context)
                    if kb_resource:
                        waiy_resources.append(kb_resource)
                else:
                    logger.warning(f"[SessionService] 未知资源类型: type={resource.type}")
                    continue

            except Exception as e:
                logger.error(f"[SessionService] 处理资源失败: type={resource.type}, resource_id={resource.resource_id}, error={e}")
                continue

        return waiy_resources

    async def _process_file_resource(
        self,
        file_id: str,
        context: AuthContext
    ):
        """
        处理文件资源

        Args:
            file_id: 文件ID
            context: 认证上下文

        Returns:
            MessageAsyncRequestResources: WaiyInfra文件资源对象
        """
        try:
            # 从文件服务获取文件信息
            file_info = file_service.get_file_info(int(file_id))

            if not file_info:
                logger.warning(f"[SessionService] 文件不存在: file_id={file_id}")
                return None

            # 检查文件权限（确保用户有权访问）
            if str(file_info.get('ali_uid')) != str(context.ali_uid):
                logger.warning(f"[SessionService] 无权访问文件: user={context.user_key}, file_id={file_id}")
                return None

            # 创建WaiyInfra客户端并构建资源对象
            waiy_client = create_waiy_infra_client()

            # 从文件名中提取文件类型（后缀名）
            file_name = file_info.get('title', '')
            file_type = ''
            if file_name and '.' in file_name:
                file_type = file_name.split('.')[-1].lower()
            
            file_resource = waiy_client.create_message_resource(
                type="file",
                address=file_info.get('download_url'),
                content=file_info.get('content'),
                file_name=file_name,
                file_size=file_info.get('file_size'),
                file_type=file_type,
                upload_time=file_info.get('gmt_created')
            )

            logger.info(f"[SessionService] 文件资源处理成功: file_id={file_id}, file_name={file_info.get('file_name')},file_resource={file_resource}")
            return file_resource

        except Exception as e:
            logger.error(f"[SessionService] 处理文件资源失败: file_id={file_id}, error={e}")
            return None

    async def _process_knowledge_base_resource(
        self,
        kb_id: str,
        context: AuthContext
    ):
        """
        处理知识库资源

        Args:
            kb_id: 知识库ID
            context: 认证上下文

        Returns:
            MessageAsyncRequestResources: WaiyInfra知识库资源对象
        """
        try:
            kb_info = knowledgebase_service.get_knowledge_base(
                kb_id=kb_id,
                owner_ali_uid=context.ali_uid,
                owner_wy_id=context.wy_id
            )

            if not kb_info:
                logger.warning(f"[SessionService] 知识库不存在: kb_id={kb_id}")
                return None

            # 创建WaiyInfra客户端并构建知识库资源对象
            #TODO 单例
            waiy_client = create_waiy_infra_client()

            kb_resource = waiy_client.create_message_resource(
                type="knowledge_base",
                kb_id=kb_id,
                query_parameters="{\"doc_memory\":\"\"}"
            )

            logger.info(f"[SessionService] 知识库资源处理成功: kb_id={kb_id}, kb_name={kb_info.name}, kb_resource={kb_resource}")
            return kb_resource

        except Exception as e:
            logger.error(f"[SessionService] 处理知识库资源失败: kb_id={kb_id}, error={e}")
            return None

    def _build_runtime_resource(
        self,
        desktop_id: Optional[str],
        auth_code: Optional[str],
        auth_context: Optional[AuthContext] = None
    ):
        """
        构建运行时资源配置

        Args:
            desktop_id: 桌面ID
            auth_code: 认证代码
            auth_context: 认证上下文，用于获取STS Token

        Returns:
            MessageAsyncRequestContextRuntimeResource: 运行时资源配置对象
        """
        try:
            from alibabacloud_wuyingaiinner20250708 import models as waiy_models

            # 根据 desktop_id 确定资源类型
            if desktop_id and desktop_id.lower() == "agentbay":
                resource_type = "agentbay"
                cloud_resource_id = None
                region = None
            elif desktop_id is None or desktop_id == "":
                resource_type = "agentbay"
                cloud_resource_id = None
                region = None
            else:
                resource_type = "desktop"
                cloud_resource_id = desktop_id
                region = None  # 可以根据需要设置具体的区域

            # 初始化 token，默认使用 auth_code
            token = auth_code or "asdf"

            # 当资源类型为 desktop 且有认证上下文时，获取 STS Token
            if resource_type == "desktop" and auth_context and desktop_id:
                sts_token = self._get_sts_token_with_cache(
                    auth_context=auth_context,
                    desktop_id=desktop_id
                )
                if sts_token:
                    token = sts_token
                    logger.info(f"[SessionService] 使用STS Token作为运行时资源token: desktop_id={desktop_id}")

            # 创建运行时资源配置对象
            runtime_resource = waiy_models.MessageAsyncRequestContextRuntimeResource(
                type=resource_type,
                token=token,
                cloud_resource_id=cloud_resource_id,
                region=region
            )

            logger.info(f"[SessionService] 构建运行时资源配置: type={resource_type}, token={'***' if token else 'None'}, cloud_resource_id={cloud_resource_id}, region={region}")

            return runtime_resource

        except Exception as e:
            logger.error(f"[SessionService] 构建运行时资源配置失败: desktop_id={desktop_id}, error={e}")
            return None

    def _get_sts_token_with_cache(
        self,
        auth_context: AuthContext,
        desktop_id: str
    ) -> Optional[str]:
        """
        获取STS Token（带缓存）

        Args:
            auth_context: 认证上下文
            desktop_id: 桌面ID

        Returns:
            str: STS Token，如果获取失败返回None
        """
        try:
            # 构建缓存键
            cache_key = f"sts_token:{auth_context.ali_uid}:{desktop_id}"

            # 初始化Redis客户端
            redis_client = RedisClient()

            # 先检查缓存
            cached_token = redis_client.get(cache_key)
            if cached_token:
                logger.info(f"[SessionService] 使用缓存的STS Token: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")
                return cached_token

            # 缓存未命中，调用API获取新的Token
            logger.info(f"[SessionService] 缓存未命中，获取新的STS Token: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")

            # 构建策略JSON
            policy = json.dumps({
                "Version": "1",
                "Resource": {
                    "Type": "Desktop",
                    "Id": desktop_id
                }
            }, separators=(',', ':'))  # 紧凑格式，无空格

            # 获取AppStream内部客户端
            appstream_client = get_appstream_inner_client()

            # 调用获取STS Token
            response = appstream_client.get_sts_token(
                end_user_id=auth_context.end_user_id,
                account_type=auth_context.account_type,
                policy=policy,
                user_ali_uid=auth_context.ali_uid
            )

            # 提取STS Token
            if response.body and response.body.token and response.body.token.sts_token:
                sts_token = response.body.token.sts_token

                # 缓存Token，有效期1小时（3600秒）
                redis_client.set(cache_key, sts_token, ex=3600)

                logger.info(f"[SessionService] STS Token获取成功并已缓存: end_user_id={auth_context.end_user_id}, response = {response} desktop_id={desktop_id}, session_id={response.body.token.session_id}")
                return sts_token
            else:
                logger.error(f"[SessionService] STS Token响应为空: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")
                return None

        except AppStreamInnerClientError as e:
            logger.error(f"[SessionService] AppStream客户端错误: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}, error={e}")
            return None
        except Exception as e:
            logger.error(f"[SessionService] 获取STS Token失败: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}, error={e}")
            return None

    def get_raw_events(
        self,
        session_id: str,
        page_size: int = 20,
        order_by:str = "desc",
        next_token: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取原始事件数据（不转换格式）
        
        Args:
            session_id: 会话ID
            page_size: 每页数量，默认20
            next_token: 下一页的令牌，用于分页
            order_by: 排序顺序
            
        Returns:
            Dict: 原始事件数据，格式为 {"events": [...], "has_more": bool, "next_token": str}
        """
        try:
            logger.info(f"[SessionService] 获取原始事件: session_id={session_id}, page_size={page_size}, next_token={next_token}")
            
            # 从Memory SDK获取历史事件
            events_result = self.memory_sdk.list_events(
                session_id=session_id,
                page_size=page_size,
                next_token=next_token
            )
            
            return events_result
            
        except Exception as e:
            logger.error(f"[SessionService] 获取原始事件失败: session_id={session_id}, error={e}")
            return None
    
    def transform_events_to_api_format(
        self,
        session_model,
        events_result: Optional[Dict[str, Any]],
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        将原始事件数据转换为API格式
        
        Args:
            session_model: 会话模型对象
            events_result: 原始事件数据
            page_size: 每页数量
            
        Returns:
            Dict: API格式的响应数据
        """
        try:
            # 构建响应数据
            response_data = {
                "SessionId": session_model.session_id,
                "AliUid": session_model.ali_uid,
                "WyId": getattr(session_model, 'wy_id', ''),
                "AgentId": session_model.agent_id,
                "Title": session_model.title,
                "GmtCreate": session_model.gmt_create.isoformat() if session_model.gmt_create else None,
                "GmtModified": session_model.gmt_modified.isoformat() if session_model.gmt_modified else None,
                "TotalRounds": 0,
                "Rounds": []
            }
            
            # 处理事件数据，按 run_id 分组为 Rounds
            if events_result and events_result.get("events"):
                # 按 run_id 分组事件
                rounds_dict = {}
                for event in events_result["events"]:
                    run_id = getattr(event, 'run_id', '')
                    if run_id not in rounds_dict:
                        rounds_dict[run_id] = {
                            "RoundId": run_id,
                            "SessionId": session_model.session_id,
                            "StartTime": None,
                            "EndTime": None,
                            "Events": []
                        }
                    
                    # 构建事件数据，保持与API文档格式一致
                    event_data = {
                        "eventid": getattr(event, 'event_id', ''),
                        "type": getattr(event, 'type', ''),
                        "timestamp": getattr(event, 'timestamp', ''),
                        "session_id": getattr(event, 'session_id', ''),
                        "ext_data": getattr(event, 'ext_data', None),
                        "run_id": getattr(event, 'run_id', '')
                    }
                    
                    # 添加可选字段
                    if hasattr(event, 'message_id') and getattr(event, 'message_id'):
                        event_data["message_id"] = getattr(event, 'message_id')
                    if hasattr(event, 'role') and getattr(event, 'role'):
                        event_data["role"] = getattr(event, 'role')
                    if hasattr(event, 'content') and getattr(event, 'content'):
                        event_data["content"] = getattr(event, 'content')
                    if hasattr(event, 'tool_call_id') and getattr(event, 'tool_call_id'):
                        event_data["tool_call_id"] = getattr(event, 'tool_call_id')
                    if hasattr(event, 'delta') and getattr(event, 'delta'):
                        event_data["delta"] = getattr(event, 'delta')
                    
                    rounds_dict[run_id]["Events"].append(event_data)
                    
                    # 更新 round 的开始和结束时间
                    timestamp = getattr(event, 'timestamp', 0)
                    if timestamp:
                        # 转换时间戳为 ISO 格式
                        from datetime import datetime
                        event_time = datetime.fromtimestamp(timestamp / 1000).isoformat() + "Z"
                        
                        if rounds_dict[run_id]["StartTime"] is None:
                            rounds_dict[run_id]["StartTime"] = event_time
                        rounds_dict[run_id]["EndTime"] = event_time
                
                # 将 rounds_dict 转换为 list
                response_data["Rounds"] = list(rounds_dict.values())
                response_data["TotalRounds"] = len(rounds_dict)
            
            # 添加分页信息 - 使用next_token分页格式
            has_more = events_result.get("has_more", False) if events_result else False
            response_next_token = events_result.get("next_token") if events_result else None
            response_data["pagination"] = {
                "pageSize": page_size,
                "hasMore": has_more,
                "nextToken": response_next_token
            }
            
            return response_data
            
        except Exception as e:
            logger.error(f"[SessionService] 转换事件格式失败: error={e}")
            raise
    
    def get_session_history(
        self,
        session_id: str,
        page_size: int = 20,
        next_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取会话历史记录
        
        Args:
            session_id: 会话ID
            page_size: 每页数量，默认20
            next_token: 下一页的令牌，用于分页
            
        Returns:
            Dict: 包含会话数据和事件的字典，格式与API文档保持一致
        """
        try:
            logger.info(f"[SessionService] 获取会话历史: session_id={session_id}, page_size={page_size}, next_token={next_token}")
            
            # 从数据库获取会话基本信息
            session_model = self.session_db_service.get_session_by_id(session_id)
            if not session_model:
                logger.warning(f"[SessionService] 会话不存在: session_id={session_id}")
                return {
                    "Code": 404,
                    "Msg": "会话不存在",
                    "Data": None
                }
            
            # 获取原始事件数据
            events_result = self.get_raw_events(session_id, page_size, next_token)
            
            # 转换为API格式
            #response_data = self.transform_events_to_api_format(session_model, events_result, page_size)
            
            return {
                "nextToken":events_result.get("next_token"),
                "data": events_result.get("events")
            }
            
        except Exception as e:
            logger.error(f"[SessionService] 获取会话历史失败: session_id={session_id}, error={e}")
            return {
                "Code": 500,
                "Msg": f"获取会话历史失败: {str(e)}",
                "Data": None
            }

    async def create_sse_stream(self, session_id: str, last_message_id: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        创建SSE流连接

        Args:
            session_id: 会话ID
            last_message_id: 最后接收到的消息ID，用于断线重连

        Yields:
            Dict[str, Any]: SSE事件数据
        """
        # 使用 SSEManager 创建 SSE 流
        async for event in self.sse_stream_manager.create_sse_stream(session_id, last_message_id):
            yield event

    def is_session_created_recently(self, session_id: str, threshold_seconds: int = 3) -> bool:
        """
        判断session_id的创建时间是否距离现在小于指定秒数
        
        Args:
            session_id: 会话ID
            threshold_seconds: 时间阈值（秒），默认3秒
            
        Returns:
            bool: True如果创建时间距离现在小于阈值，False否则
        """
        try:
            from datetime import datetime, timezone
            
            session_model = self.session_db_service.get_session_by_id(session_id)
            if not session_model or not session_model.gmt_create:
                return False
            
            # 获取会话创建时间
            create_time = session_model.gmt_create

            # 获取当前时间
            now = datetime.now()
            
            # 计算时间差（保留小数精度，更准确）
            time_diff = (now - create_time).total_seconds()
            
            logger.info(f"[SessionService] 会话创建时间检查: session_id={session_id}, "
                       f"create_time={create_time}, now={now}, diff={time_diff:.3f}s, threshold={threshold_seconds}s")
            
            return time_diff < threshold_seconds
            
        except Exception as e:
            logger.error(f"[SessionService] 检查会话创建时间失败: session_id={session_id}, error={e}")
            return False
    
    def load_session_from_db(self, session_id: str):
        """从数据库加载Session - 为SSEManager提供接口"""
        try:
            session_model = self.session_db_service.get_session_by_id(session_id)
            if not session_model:
                return None
            return session_model
        except Exception as e:
            logger.error(f"[SessionService] 从数据库加载Session失败: session_id={session_id}, error={e}")
            return None

    def setup_session_events(self, session):
        """设置Session事件监听 - 为SSEManager提供接口"""
        # 这里可以添加事件监听逻辑，暂时为空
        pass

    async def _generate_session_title_async(self, session_id: str, prompt: str):
        """
        异步生成会话标题
        
        Args:
            session_id: 会话ID
            prompt: 用户消息内容
        """
        try:
            logger.info(f"[SessionService] 开始生成会话标题: session_id={session_id}")
            
            # 创建WaiyInfra客户端
            waiy_client = create_waiy_infra_client()
            
            # 调用同步消息处理方法生成标题
            response = waiy_client.message(
                app_id="chat_namer",
                message=prompt
            )

            # 提取生成的标题
            title = None
            if response and response.body:
                try:
                    response_data = None
                    if hasattr(response.body, 'response'):
                        response_data = response.body.response
                    if response_data:
                        title = str(response_data).strip()
                        logger.info(f"[SessionService] 成功提取标题: {title}")
                    else:
                        logger.warning(f"[SessionService] 未找到 response 字段，响应体: {response.body}")

                except Exception as parse_error:
                    logger.error(f"[SessionService] 解析响应失败: {parse_error}, 响应体: {response.body}")

            if title and title.strip():
                # 清理和验证标题
                title = title.strip()
                if len(title) > 200:
                    title = title[:200]
                
                # 更新数据库中的标题
                success = self.session_db_service.update_session_title(session_id, title)
                
                if success:
                    logger.info(f"[SessionService] 会话标题生成成功: session_id={session_id}, title={title}")
                else:
                    logger.error(f"[SessionService] 更新会话标题失败: session_id={session_id}")
            else:
                logger.warning(f"[SessionService] 未能提取有效标题: session_id={session_id}, response={response}")
                
        except WaiyInfraClientError as e:
            logger.error(f"[SessionService] 标题生成WaiyInfra客户端错误: session_id={session_id}, error={e}")
        except Exception as e:
            logger.error(f"[SessionService] 异步生成会话标题失败: session_id={session_id}, error={e}")


# 创建全局实例
session_service = SessionService()
