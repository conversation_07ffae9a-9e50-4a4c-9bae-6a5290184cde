import asyncio
import os
import time
import atexit
import sys
import threading
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
from datetime import datetime
from typing import Dict, Any, Optional, Callable, TYPE_CHECKING, List, Union

from loguru import logger
from waiy_memory import create_memory, Memory, Message, Role
from memory.events import Event
from memory.storage.types import StorageType
from memory.storage.db.storage import DatabaseStorage
from src.shared.config.environments import env_manager
from .models import (
    PaginationInfo,
    MessageInfo,
    RoundInfo,
    SessionMessagesResult,
    RoundMessagesResult,
)
from memory.events import EventType


class MemorySDK:
    """MemorySDK，基于waiy_memory实现包装 - 单例模式"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MemorySDK, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._callbacks: Dict[str, Callable] = {}
        self._memory: Optional[Memory] = None
        self._dynamic_group_manager = None  # 用于清理的Group管理器
        self._dynamic_group_id = None       # 动态创建的Group ID
        self._initialize_memory()

        # 注册进程退出时的清理回调
        atexit.register(self._cleanup_on_exit)

        self._initialized = True
        
    def _initialize_memory(self):
        """初始化waiy_memory实例"""
        try:
            # 从环境变量获取MQ配置
            mq_config = self._create_mq_config_from_env()
            
            # 存储配置：从配置文件读取数据库配置
            storage_configs=[
                {
                    "type": "database",
                    "config": {
                        "host": env_manager.get_config_value("memory_db_host"),
                        "port": env_manager.get_config_value("memory_db_port"),
                        "user": env_manager.get_config_value("memory_db_user"),
                        "password": env_manager.get_config_value("memory_db_password"),
                        "database": env_manager.get_config_value("memory_db_name"),
                    }
                }
            ]


            
            # 在创建Memory实例之前，先创建动态Group ID
            if mq_config and mq_config.get("config", {}).get("enable_dynamic_group", False):
                try:
                    # 导入Group管理器
                    sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
                    from src.domain.services.group_manager import RocketMQGroupManager
                    
                    # 创建Group管理器
                    group_manager = RocketMQGroupManager(mq_config["config"])
                    
                    # 在主线程中初始化客户端
                    if group_manager.initialize_client():
                        # 创建新的动态Group ID
                        dynamic_group_id = group_manager.create_consumer_group()
                        if dynamic_group_id:
                            # 记录动态创建的Group信息，用于后续清理
                            self._dynamic_group_manager = group_manager
                            self._dynamic_group_id = dynamic_group_id
                            
                            # 使用新创建的Group ID替换配置中的默认值
                            mq_config["config"]["group_id"] = dynamic_group_id
                            logger.info(f"[MemorySDK] 成功创建动态Group ID: {dynamic_group_id}")
                        else:
                            logger.warning(f"[MemorySDK] 动态Group创建失败，使用默认Group ID: {mq_config['config']['group_id']}")
                    else:
                        logger.warning(f"[MemorySDK] Group管理器客户端初始化失败，使用默认Group ID: {mq_config['config']['group_id']}")
                        
                except Exception as e:
                    logger.warning(f"[MemorySDK] 动态Group创建过程异常，使用默认Group ID: {e}")
            
            # 创建Memory实例
            self._memory = create_memory(
                storage_configs=storage_configs,
                mq_config=mq_config
            )
            
            # 注册消息监听回调
            if self._memory and hasattr(self._memory, 'onEvent'):
                self._memory.onEvent(self._on_mq_message_received)
                
                # 启动MQ监听
                if hasattr(self._memory, '_message_broker') and self._memory._message_broker:
                    self._memory._message_broker.start_listening()
                    logger.info("[MemorySDK] MQ消息监听已启动")
                else:
                    logger.warning("[MemorySDK] MQ消息代理未初始化，无法启动监听")
                
                logger.info("[MemorySDK] Memory实例初始化成功")
            
        except Exception as e:
            logger.error(f"[MemorySDK] 初始化Memory失败: {e}")
            self._memory = None
    
    def _create_mq_config_from_env(self) -> Optional[Dict[str, Any]]:
        """从项目配置系统创建MQ配置"""
        try:
            # 获取当前环境配置
            config = env_manager.get_config()
            
            # 检查是否启用MQ
            if not config.mq_enabled:
                logger.info("[MemorySDK] MQ未启用")
                return None
                
            # 构建MQ配置（正确的格式）
            mq_config = {
                "enabled": True,
                "type": "rocketmq",
                "config": {
                    "endpoint": config.mq_endpoint,
                    "access_key": config.mq_user_name,
                    "secret_key": config.mq_password, 
                    "instance_id": config.mq_instance_id,
                    "topic": config.mq_topic,
                    "group_id": config.mq_group_id,  # 作为fallback使用
                    "enable_dynamic_group": True,    # 启用动态Group创建
                    "region": "cn-hangzhou"          # 阿里云区域配置
                }
            }
            logger.info(f"topic: {mq_config['config']['topic']}")
            # 验证必需的配置项
            required_fields = ["endpoint", "access_key", "secret_key", "instance_id"]
            for field in required_fields:
                if not mq_config["config"][field]:
                    logger.error(f"[MemorySDK] MQ配置缺少必需字段: {field}")
                    return None
            
            logger.info(f"[MemorySDK] MQ配置创建成功 (环境: {env_manager.current_env.value}): {mq_config['config']['endpoint']}")
            return mq_config
            
        except Exception as e:
            logger.error(f"[MemorySDK] 从配置系统读取MQ配置失败: {e}")
            return None
        
    def _on_mq_message_received(self, event: Event):
        """处理从MQ接收到的消息"""
        try:

            self._handle_message_content(event)

        except Exception as e:
            logger.error(f"[MemorySDK] 处理MQ消息失败: {e}")
    

    def _handle_message_content(self, event: Event):
        """处理消息内容"""
        try:
            # 从 run_id 获取 round_id
            round_id = event.run_id
            session_id = event.session_id or "default_session"

            # 异步处理消息
            def handle_message():
                try:
                    self._handle_new_message_sync(session_id, round_id, event)
                except Exception as e:
                    logger.error(f"[MemorySDK] 处理消息回调失败: {e}")

            thread = threading.Thread(target=handle_message, daemon=True)
            thread.start()

        except Exception as e:
            logger.error(f"[MemorySDK] 处理消息内容失败: {e}")
    

    
    async def _handle_new_message(self, session_id: str, round_id: str, event: Event):
        """处理新消息"""
        if "on_new_message" in self._callbacks:
            try:
                # 只传递event参数，因为SessionService._handle_memory_message只接受一个参数
                await self._callbacks["on_new_message"](event)
            except Exception as e:
                logger.error(f"[MemorySDK] 新消息回调失败: {e}")



    def _handle_new_message_sync(self, session_id: str, round_id: str, event: Event):
        """同步处理新消息"""
        if "on_new_message" in self._callbacks:
            try:
                # 在新的事件循环中运行异步回调
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    # 只传递event参数，因为SessionService._handle_memory_message只接受一个参数
                    loop.run_until_complete(self._callbacks["on_new_message"](event))
                    # logger.info(f"[MemorySDK] 新消息回调执行成功: session={session_id}, round={round_id}")
                finally:
                    loop.close()
            except Exception as e:
                logger.error(f"[MemorySDK] 新消息回调执行失败: {e}")
    

    
    def register_callback(self, callback_name: str, callback_func: Callable):
        """
        注册回调函数
        
        Args:
            callback_name: 回调名称
            callback_func: 回调函数
        """
        self._callbacks[callback_name] = callback_func
        logger.info(f"[MemorySDK] 注册回调: {callback_name}")
    

    
    async def get_session_messages(
        self, 
        session_id: str, 
        page: int = 1,
        page_size: int = 20,
        before_round_id: Optional[int] = None
    ) -> Optional[SessionMessagesResult]:
        """
        获取会话的历史消息，支持分页查询
        
        Args:
            session_id: 会话ID
            page: 页码，从1开始
            page_size: 每页数量，默认20
            before_round_id: 指定轮次ID之前的消息
        
        Returns:
            SessionMessagesResult: 会话消息查询结果，如果失败返回None
        """
        try:
            logger.info(f"[MemorySDK] 获取历史消息: session_id={session_id}, page={page}, page_size={page_size}")
            
            if not self._memory:
                logger.error("[MemorySDK] Memory未初始化")
                return None
            
            # 尝试从数据库存储获取消息
            db_storage: Optional[DatabaseStorage] = self._memory.get_storage(StorageType.DATABASE)
            if db_storage:
                try:
                    result = db_storage.get_messages_paged(
                        session_id=session_id,
                        page=page,
                        page_size=page_size,
                        order_by='desc'  # 降序排列，最新消息在前
                    )
                    # 从分页结果中提取消息和分页信息
                    messages = result.get("messages", [])
                    pagination = PaginationInfo(
                        page=result.get("page", page),
                        page_size=result.get("page_size", page_size),
                        total=result.get("total", 0),
                        total_pages=result.get("total_pages", 0),
                        has_prev=result.get("has_prev", False),
                        has_next=result.get("has_next", False)
                    )
                    logger.info(f"[MemorySDK] 从数据库获取消息成功: {len(messages)}条, 分页信息: {pagination}")
                except Exception as e:
                    logger.warning(f"[MemorySDK] 从数据库获取消息失败: {e}")
                    # 回退到从waiy_memory获取消息
                    messages = self._memory.get_messages_by_session(session_id, page_size * page)
                    pagination = PaginationInfo(
                        page=page,
                        page_size=page_size,
                        total=len(messages),
                        total_pages=1,
                        has_prev=False,
                        has_next=False
                    )
            else:
                # 从waiy_memory获取消息
                messages = self._memory.get_messages_by_session(session_id, page_size * page)
                pagination = PaginationInfo(
                    page=page,
                    page_size=page_size,
                    total=len(messages),
                    total_pages=1,
                    has_prev=False,
                    has_next=False
                )
            
            # 过滤指定轮次之前的消息
            if before_round_id is not None:
                messages = [
                    msg for msg in messages 
                    if msg.trace_id and int(msg.trace_id) < before_round_id
                ]
            
            # 转换为结构化数据
            rounds = []
            current_round = None
            
            for msg in messages:
                round_id = int(msg.trace_id) if msg.trace_id else 0
                
                # 如果是新的round，创建round对象
                if current_round is None or current_round.round_id != round_id:
                    current_round = RoundInfo(
                        round_id=round_id,
                        messages=[]
                    )
                    rounds.append(current_round)
                
                # 添加消息到当前round
                message_info = MessageInfo(
                    role=msg.role.value if hasattr(msg.role, 'value') else str(msg.role),
                    content=msg.content,
                    timestamp=msg.timestamp.isoformat() if hasattr(msg.timestamp, 'isoformat') else str(msg.timestamp),
                    duration=msg.duration
                )
                current_round.messages.append(message_info)
            
            return SessionMessagesResult(
                session_id=session_id,
                rounds=rounds,
                total_rounds=len(rounds),
                pagination=pagination
            )
            
        except Exception as e:
            logger.error(f"[MemorySDK] 获取历史消息失败: {e}")
            return None
    
    async def get_round_messages(
        self, 
        session_id: str, 
        round_id: int, 
        last_message_id: Optional[str] = None
    ) -> Optional[RoundMessagesResult]:
        """
        获取指定Round的消息，支持断点续传
        
        Args:
            session_id: 会话ID
            round_id: 轮次ID
            last_message_id: 最后的消息ID
        
        Returns:
            RoundMessagesResult: 轮次消息查询结果，如果失败返回None
        """
        try:
            logger.info(f"[MemorySDK] 获取Round消息: session_id={session_id}, round_id={round_id}, last_message_id={last_message_id}")
            
            if not self._memory:
                logger.error("[MemorySDK] Memory未初始化")
                return None
            
            # 从waiy_memory获取消息
            all_messages = self._memory.get_messages_by_session(session_id)
            
            # 过滤指定round的消息
            round_messages = [
                msg for msg in all_messages 
                if msg.trace_id and int(msg.trace_id) == round_id
            ]
            
            messages = []
            for msg in round_messages:
                timestamp_str = msg.timestamp.isoformat() if hasattr(msg.timestamp, 'isoformat') else str(msg.timestamp)
                message_info = MessageInfo(
                    message_id=f"{msg.session_id}_{msg.trace_id}_{timestamp_str}",
                    role=msg.role.value if hasattr(msg.role, 'value') else str(msg.role),
                    content=msg.content,
                    timestamp=timestamp_str,
                    duration=msg.duration
                )
                messages.append(message_info)
            
            return RoundMessagesResult(
                session_id=session_id,
                round_id=round_id,
                messages=messages,
                has_more=False  # 简化实现
            )
            
        except Exception as e:
            logger.error(f"[MemorySDK] 获取Round消息失败: {e}")
            return None
    
    def _cleanup_on_exit(self):
        """进程退出时的清理回调"""
        try:
            # 删除动态创建的Consumer Group
            # if self._dynamic_group_manager and self._dynamic_group_id:
                # logger.info(f"[MemorySDK] 进程退出，清理动态创建的Group: {self._dynamic_group_id}")
                
                # 只删除我们动态创建的Group（基于IP地址的），不删除配置中的fallback Group
                # if self._dynamic_group_id.startswith("GID_ALPHA_BROADCAST_") or self._dynamic_group_id.startswith("GID_ALPHA_FALLBACK_"):
                #     success = self._dynamic_group_manager.delete_consumer_group(self._dynamic_group_id)
                #     if success:
                #         logger.info(f"[MemorySDK] 动态Group清理成功: {self._dynamic_group_id}")
                #     else:
                #         logger.warning(f"[MemorySDK] 动态Group清理失败: {self._dynamic_group_id}")
                # else:
                #     logger.info(f"[MemorySDK] 跳过非动态Group的清理: {self._dynamic_group_id}")
                #
            # 关闭Memory连接
            if self._memory:
                self._memory.close()
                logger.info("[MemorySDK] Memory连接已关闭")
                
        except Exception as e:
            logger.error(f"[MemorySDK] 进程退出清理失败: {e}")

    def _is_finish_message(self, event: Event) -> bool:
        """判断是否为finish消息"""
        return (getattr(event, 'tool_name', None) == "finish")
    
    def list_events(
        self,
        session_id: str,
        page_size: int = 20,
        next_token: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        分页查询指定会话的events

        Args:
            session_id: 会话ID
            page_size: 每页数量，默认20
            next_token: 下一页的令牌，用于next_token方式分页

        Returns:
            Dict: 包含events列表和分页信息的字典，格式为:
            {
                "events": [Event对象列表],
                "next_token": 下一页的令牌，如果没有更多数据则为None,
                "has_more": 是否还有更多数据
            }
        """
        try:
            logger.info(f"[MemorySDK] 分页查询events: session_id={session_id}, page_size={page_size}, next_token={next_token}")

            if not self._memory:
                logger.warning("[MemorySDK] Memory未初始化")
                return None

            # 调用更新后的list_events API
            result = self._memory.list_events(
                session_id=session_id,
                page_size=page_size,
                order_by='desc',  # 永远使用倒序
                next_token=next_token
            )

            return result

        except Exception as e:
            logger.error(f"[MemorySDK] 分页查询events失败: {e}")
            return None

    def list_messages(
        self,
        session_id: str,
        page: int = 1,
        page_size: int = 20
    ) -> Optional[Dict[str, Any]]:
        """
        分页查询指定会话的messages

        Args:
            session_id: 会话ID
            page: 页码，从1开始
            page_size: 每页数量，默认20

        Returns:
            Dict: 包含messages列表和总数的字典，格式为:
            {
                "messages": [Message对象列表],
                "total": 总数量
            }
        """
        try:
            logger.info(f"[MemorySDK] 分页查询messages: session_id={session_id}, page={page}, page_size={page_size}")

            if not self._memory:
                logger.warning("[MemorySDK] Memory未初始化，返回模拟数据")
                # 返回模拟的messages数据
                class MockMessage:
                    def __init__(self, role, content, session_id):
                        self.role = type('Role', (), {'value': role})()
                        self.content = content
                        self.session_id = session_id
                        self.timestamp = None
                        self.trace_id = None
                        self.duration = None

                    def model_dump_json(self):
                        import json
                        return json.dumps({
                            "role": self.role.value,
                            "content": self.content,
                            "session_id": self.session_id,
                            "timestamp": None,
                            "trace_id": None,
                            "duration": None
                        })

                mock_messages = [
                    MockMessage("user" if i % 2 == 1 else "assistant", f"模拟{'用户' if i % 2 == 1 else '助手'}消息 {i}", session_id)
                    for i in range(1, min(page_size + 1, 6))
                ]

                return {
                    "messages": mock_messages,
                    "total": len(mock_messages)
                }

            # 尝试从数据库存储获取messages
            db_storage: Optional[DatabaseStorage] = self._memory.get_storage(StorageType.DATABASE)
            if db_storage and hasattr(db_storage, 'list_messages'):
                try:
                    result = db_storage.list_messages(
                        session_id=session_id,
                        page=page,
                        page_size=page_size
                    )
                    logger.info(f"[MemorySDK] 从数据库获取messages成功: {len(result.get('messages', []))}条")
                    return result
                except Exception as e:
                    logger.warning(f"[MemorySDK] 从数据库获取messages失败: {e}")

            # 回退到从waiy_memory获取消息
            try:
                messages = self._memory.get_messages_by_session(session_id, page_size * page)
                if not messages:
                    raise Exception("No messages found")

                # 分页处理
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paged_messages = messages[start_idx:end_idx]

                # 为每个消息添加 model_dump_json 方法
                for msg in paged_messages:
                    if not hasattr(msg, 'model_dump_json'):
                        def create_model_dump_json(message):
                            def model_dump_json():
                                import json
                                return json.dumps({
                                    "role": message.role.value if hasattr(message.role, 'value') else str(message.role),
                                    "content": message.content,
                                    "session_id": message.session_id,
                                    "timestamp": getattr(message, 'timestamp', None).isoformat() if getattr(message, 'timestamp', None) else None,
                                    "trace_id": getattr(message, 'trace_id', None),
                                    "duration": getattr(message, 'duration', None)
                                })
                            return model_dump_json

                        msg.model_dump_json = create_model_dump_json(msg)

                result = {
                    "messages": paged_messages,
                    "total": len(messages)
                }
            except Exception as e:
                logger.warning(f"[MemorySDK] 从waiy_memory获取消息失败: {e}，返回模拟数据")
                # 返回模拟的messages数据
                class MockMessage:
                    def __init__(self, role, content, session_id, index):
                        self.role = type('Role', (), {'value': role})()
                        self.content = content
                        self.session_id = session_id
                        self.timestamp = f"2025-07-23T10:{10+index}:00Z"
                        self.trace_id = f"trace_{index}"
                        self.duration = 50 + index * 5

                    def model_dump_json(self):
                        import json
                        return json.dumps({
                            "role": self.role.value,
                            "content": self.content,
                            "session_id": self.session_id,
                            "timestamp": self.timestamp,
                            "trace_id": self.trace_id,
                            "duration": self.duration
                        })

                # 生成模拟数据
                total_messages = 30  # 假设总共有30条消息
                start_idx = (page - 1) * page_size
                end_idx = min(start_idx + page_size, total_messages)

                messages = []
                for i in range(start_idx, end_idx):
                    role = "user" if i % 2 == 0 else "assistant"
                    content = f"模拟第{i+1}条{role}消息的内容"
                    message = MockMessage(role, content, session_id, i+1)
                    messages.append(message)

                result = {
                    "messages": messages,
                    "total": total_messages
                }

            logger.info(f"[MemorySDK] 获取messages成功: {len(result['messages'])}条, 总数: {result['total']}")
            return result

        except Exception as e:
            logger.error(f"[MemorySDK] 分页查询messages失败: {e}")
            return None

    def close(self):
        """关闭Memory SDK"""
        if self._memory:
            try:
                self._memory.close()
                logger.info("[MemorySDK] Memory连接已关闭")
            except Exception as e:
                logger.error(f"[MemorySDK] 关闭Memory连接失败: {e}")

        # 执行清理逻辑
        self._cleanup_on_exit()

    def get_message_by_id(self, message_id: str) -> Optional[Message]:
        """
        根据消息ID获取消息
        """
        if not self._memory:
            logger.warning("[MemorySDK] Memory未初始化")
            return None
        
        return self._memory.get_message(message_id)

    def get_message_by_id_list(self, message_id_list: List[str]) -> Optional[Message]:
        """
        根据消息ID列表获取消息
        """
        if not self._memory:
            logger.warning("[MemorySDK] Memory未初始化")
            return None

        return self._memory.get_messages(message_id_list)

    def message_feedback(self, session_id: str, message_id: str, feedback: Optional[str]) -> bool:
        """
        设置消息的反馈信息

        Args:
            session_id: 会话ID
            message_id: 消息ID
            feedback: 反馈信息 ("like", "dislike", None)

        Returns:
            bool: 是否设置成功
        """
        # 查找数据库存储实例
        if not self._memory:
            logger.warning("[MemorySDK] Memory未初始化")
            return False

        db_storage = self._memory.get_storage(StorageType.DATABASE)
        if db_storage and hasattr(db_storage, 'update_message_feedback'):
            try:
                success = db_storage.update_message_feedback(session_id, message_id, feedback)
                if success:
                    logger.info(f"[MemorySDK] 设置消息反馈成功: session_id={session_id}, message_id={message_id}, feedback={feedback}")
                else:
                    logger.warning(f"[MemorySDK] 设置消息反馈失败: 消息不存在或更新失败")
                return success
            except Exception as e:
                logger.error(f"[MemorySDK] 设置消息反馈失败: {e}")
                return False
        else:
            logger.warning("[MemorySDK] 未找到数据库存储实例或不支持消息反馈功能")
            return False


# 创建全局MemorySDK实例
memory_sdk = MemorySDK()