#!/usr/bin/env python3
"""
Alpha Service 启动脚本
"""

# 在导入任何其他模块之前，先设置早期日志拦截
import src.shared.logging.early_intercept

import asyncio
import uvicorn

# 初始化日志配置
from src.shared.logging.logger import logger

async def start_service():
    """启动服务"""
    try:
        # 导入应用
        from src.presentation.api.pythonic_server import pythonic_app as app
        
        logger.info("🚀 Alpha Service 启动中...")
        
        # 配置uvicorn
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False,
            access_log=False  # 禁用访问日志，避免健康检查日志
        )
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(start_service()) 