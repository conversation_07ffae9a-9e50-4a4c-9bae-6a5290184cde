#!/usr/bin/env python3
"""
测试AuthContext的account_type功能
验证account_type字段是否正确添加和使用
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.domain.services.auth_service import AuthContext, AuthService


def test_auth_context_with_account_type():
    """测试AuthContext包含account_type字段"""
    logger.info("=== 测试AuthContext包含account_type字段 ===")
    
    try:
        # 测试基本创建
        context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        # 验证属性
        assert context.ali_uid == 123456
        assert context.wy_id == "test_user"
        assert context.end_user_id == "test_end_user"
        assert context.account_type == "ALIYUN"
        
        logger.success("✅ AuthContext基本属性验证通过")
        
        # 测试to_dict方法
        context_dict = context.to_dict()
        expected_keys = {"ali_uid", "wy_id", "end_user_id", "account_type", "user_key"}
        actual_keys = set(context_dict.keys())
        
        assert expected_keys == actual_keys, f"字典键不匹配: 期望 {expected_keys}, 实际 {actual_keys}"
        assert context_dict["account_type"] == "ALIYUN"
        
        logger.success("✅ AuthContext.to_dict()方法验证通过")
        logger.info(f"上下文字典: {context_dict}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ AuthContext基本功能测试失败: {e}")
        return False


def test_auth_context_optional_account_type():
    """测试account_type为可选参数"""
    logger.info("=== 测试account_type为可选参数 ===")
    
    try:
        # 测试不提供account_type
        context = AuthContext(
            ali_uid=123456,
            wy_id="test_user"
        )
        
        assert context.account_type is None
        logger.success("✅ 可选account_type参数验证通过")
        
        # 测试to_dict包含None值
        context_dict = context.to_dict()
        assert "account_type" in context_dict
        assert context_dict["account_type"] is None
        
        logger.success("✅ 可选参数的to_dict()验证通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 可选参数测试失败: {e}")
        return False


def test_auth_service_create_context():
    """测试AuthService.create_auth_context方法"""
    logger.info("=== 测试AuthService.create_auth_context方法 ===")
    
    try:
        auth_service = AuthService()
        
        # 测试包含account_type的创建
        context = auth_service.create_auth_context(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        assert context.ali_uid == 123456
        assert context.wy_id == "test_user"
        assert context.end_user_id == "test_end_user"
        assert context.account_type == "ALIYUN"
        
        logger.success("✅ AuthService.create_auth_context()验证通过")
        
        # 测试不提供account_type
        context2 = auth_service.create_auth_context(
            ali_uid=789012,
            wy_id="test_user2"
        )
        
        assert context2.account_type is None
        logger.success("✅ AuthService可选参数验证通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ AuthService测试失败: {e}")
        return False


def test_different_account_types():
    """测试不同的account_type值"""
    logger.info("=== 测试不同的account_type值 ===")
    
    try:
        account_types = ["ALIYUN", "SIMPLE", "ENTERPRISE", None, ""]
        
        for account_type in account_types:
            context = AuthContext(
                ali_uid=123456,
                wy_id="test_user",
                account_type=account_type
            )
            
            assert context.account_type == account_type
            
            context_dict = context.to_dict()
            assert context_dict["account_type"] == account_type
            
            logger.info(f"✅ account_type='{account_type}' 验证通过")
        
        logger.success("✅ 不同account_type值测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 不同account_type值测试失败: {e}")
        return False


def test_auth_context_compatibility():
    """测试向后兼容性"""
    logger.info("=== 测试向后兼容性 ===")
    
    try:
        # 测试旧的创建方式仍然有效
        context = AuthContext(123456, "test_user")
        
        assert context.ali_uid == 123456
        assert context.wy_id == "test_user"
        assert context.end_user_id == "test_user"  # 默认值
        assert context.account_type is None  # 新字段默认为None
        
        logger.success("✅ 向后兼容性验证通过")
        
        # 测试位置参数和关键字参数混合
        context2 = AuthContext(123456, "test_user", account_type="ALIYUN")
        
        assert context2.account_type == "ALIYUN"
        logger.success("✅ 混合参数方式验证通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 向后兼容性测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始AuthContext account_type功能测试...")
    
    success_count = 0
    total_tests = 5
    
    # 测试基本功能
    if test_auth_context_with_account_type():
        success_count += 1
        logger.success("✅ AuthContext基本功能测试通过")
    else:
        logger.error("❌ AuthContext基本功能测试失败")
    
    # 测试可选参数
    if test_auth_context_optional_account_type():
        success_count += 1
        logger.success("✅ 可选参数测试通过")
    else:
        logger.error("❌ 可选参数测试失败")
    
    # 测试AuthService
    if test_auth_service_create_context():
        success_count += 1
        logger.success("✅ AuthService测试通过")
    else:
        logger.error("❌ AuthService测试失败")
    
    # 测试不同account_type值
    if test_different_account_types():
        success_count += 1
        logger.success("✅ 不同account_type值测试通过")
    else:
        logger.error("❌ 不同account_type值测试失败")
    
    # 测试向后兼容性
    if test_auth_context_compatibility():
        success_count += 1
        logger.success("✅ 向后兼容性测试通过")
    else:
        logger.error("❌ 向后兼容性测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有AuthContext account_type功能测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
