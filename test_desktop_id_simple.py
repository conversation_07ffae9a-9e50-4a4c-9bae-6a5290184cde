#!/usr/bin/env python3
"""
简化版 desktop_id 测试脚本
专门测试 /api/sessions/send 接口的 desktop_id 功能
"""

import asyncio
import httpx
import json
from loguru import logger
import sys

# ==================== 配置区域 ====================
# 尝试导入配置文件，如果不存在则使用默认配置

try:
    from test_config import *
    logger.info("✅ 已加载 test_config.py 配置文件")
except ImportError:
    logger.warning("⚠️ 未找到 test_config.py，使用默认配置")
    BASE_URL = "http://localhost:8000"
    LOGIN_TOKEN = "stsv2xithrJ2mAxZAe3dQXiyZxu5wJnodgOKEkP1AV9DAURkxHkxouIhH0zy1kibMJLH5djDTTnixaI3QAjwCukzmuX+HRLz6KyxdiUzxgNe3ZsujCVUi3Hl1lmlrxTxFN+vi9h3hhPLfiX5qJsFmguZQHTY0DLal22oxCiOt3iDkNZouFpTC5BsDyX/mfhSOkv7GUmtNLCUQs7pCreryv+naiy9HSaqIXhqyYPKVROsfdh1qLOs4WOrW1r9Pvt+AQrWOpjb7/lNzXWawiBVEOgtjOyBxqt2aKyvAJTywykx8X30FJGZYVWvSC5N7Guer0/6An8ODMKS8OfhPNXjIOlna5cG6rFTk0OLqhTNMOdRnDeCmX6jne5u3ypdJunIZ4sALroGy9CVT72Hfn5E+a/gSfRoIaBbVp6E5NIYuKzoTocw="
    TEST_DESKTOP_IDS = [
        "desktop_001",
        "agentbay",
        "desktop_test_123",
        None
    ]
    REQUEST_TIMEOUT = 30.0
    TEST_INTERVAL = 1.0

# 兼容性处理
DESKTOP_IDS = globals().get('TEST_DESKTOP_IDS', globals().get('DESKTOP_IDS', []))

# ==================== 测试代码 ====================

async def send_test_message(desktop_id=None, session_id=None):
    """发送测试消息"""
    url = f"{BASE_URL}/api/sessions/send?LoginToken=stsv2xithrJ2mAxZAe3dQXiyZxu5wJnodgOKEkP1AV9DAURkxHkxouIhH0zy1kibMJLH5djDTTnixaI3QAjwCukzmuX+HRLz6KyxdiUzxgNe3ZsujCVUi3Hl1lmlrxTxFN+vi9h3hhPLfiX5qJsFmguZQHTY0DLal22oxCiOt3iDkNZouFpTC5BsDyX/mfhSOkv7GUmtNLCUQs7pCreryv+naiy9HSaqIXhqyYPKVROsfdh1qLOs4WOrW1r9Pvt+AQrWOpjb7/lNzXWawiBVEOgtjOyBxqt2aKyvAJTywykx8X30FJGZYVWvSC5N7Guer0/6An8ODMKS8OfhPNXjIOlna5cG6rFTk0OLqhTNMOdRnDeCmX6jne5u3ypdJunIZ4sALroGy9CVT72Hfn5E+a/gSfRoIaBbVp6E5NIYuKzoTocw=&regionId=cn-hangzhou&sessionId=8ceb3e44-ee3b-4a82-beb1-8117b5bd2dde"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {LOGIN_TOKEN}"
    }
    
    # 构建请求数据
    data = {
        "Prompt": f"测试消息 - Desktop ID: {desktop_id or 'None'}",
        "AgentId": "test_agent"
    }
    
    if session_id:
        data["SessionId"] = session_id
    if desktop_id:
        data["DesktopId"] = desktop_id

    logger.info(f"发送请求: Desktop ID = {desktop_id}")
    logger.info(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, headers=headers, json=data)
            
            logger.info(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.success(f"✅ 请求成功")
                logger.info(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 提取会话信息
                data_field = result.get("Data") or result.get("data")
                if data_field:
                    session_id = data_field.get("SessionId") or data_field.get("session_id")
                    round_id = data_field.get("RoundId") or data_field.get("round_id")
                    logger.info(f"会话ID: {session_id}")
                    logger.info(f"轮次ID: {round_id}")
                    return session_id
                
            else:
                logger.error(f"❌ 请求失败: {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                
    except Exception as e:
        logger.error(f"❌ 请求异常: {e}")
    
    return None


async def test_desktop_ids():
    """测试不同的桌面ID"""
    logger.info("=== 开始测试不同的桌面ID ===")
    
    results = []
    
    for i, desktop_id in enumerate(DESKTOP_IDS, 1):
        logger.info(f"\n{i}. 测试桌面ID: {desktop_id or 'None'}")
        logger.info("-" * 40)
        
        session_id = await send_test_message(desktop_id=desktop_id)
        
        results.append({
            "desktop_id": desktop_id,
            "session_id": session_id,
            "success": session_id is not None
        })
        
        # 测试间隔
        await asyncio.sleep(1)
    
    # 总结结果
    logger.info("\n" + "="*50)
    logger.info("测试结果总结:")
    
    for i, result in enumerate(results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        logger.info(f"{i}. Desktop ID: {result['desktop_id'] or 'None'} - {status}")
        if result["session_id"]:
            logger.info(f"   会话ID: {result['session_id']}")
    
    success_count = sum(1 for r in results if r["success"])
    logger.info(f"\n成功: {success_count}/{len(results)}")
    
    return results


async def test_session_continuation():
    """测试会话延续（使用相同的桌面ID）"""
    logger.info("\n=== 测试会话延续 ===")
    
    desktop_id = "desktop_continuation_test"
    
    # 第一条消息
    logger.info("1. 发送第一条消息（创建新会话）")
    session_id = await send_test_message(desktop_id=desktop_id)
    
    if not session_id:
        logger.error("❌ 无法创建会话")
        return False
    
    await asyncio.sleep(2)
    
    # 第二条消息（延续会话）
    logger.info("2. 发送第二条消息（延续会话）")
    second_session_id = await send_test_message(
        desktop_id=desktop_id, 
        session_id=session_id
    )
    
    if second_session_id == session_id:
        logger.success("✅ 会话延续成功")
        return True
    else:
        logger.error("❌ 会话延续失败")
        return False


def check_configuration():
    """检查配置"""
    logger.info("=== 检查配置 ===")
    
    if LOGIN_TOKEN == "YOUR_LOGIN_TOKEN_HERE":
        logger.error("❌ 请先设置登录令牌！")
        logger.info("请修改脚本中的 LOGIN_TOKEN 变量")
        return False
    
    logger.info(f"API地址: {BASE_URL}")
    logger.info(f"登录令牌: {LOGIN_TOKEN[:10]}...")
    logger.success("✅ 配置检查通过")
    return True


async def main():
    """主函数"""
    logger.info("🚀 开始 Desktop ID 功能测试")
    
    # 检查配置
    if not check_configuration():
        return False
    
    try:
        # 测试不同的桌面ID
        results = await test_desktop_ids()
        
        # 测试会话延续
        continuation_success = await test_session_continuation()
        
        # 最终总结
        logger.info("\n" + "="*60)
        logger.info("🎯 最终测试总结")
        
        success_count = sum(1 for r in results if r["success"])
        total_tests = len(results) + (1 if continuation_success else 0)
        
        logger.info(f"桌面ID测试: {success_count}/{len(results)} 成功")
        logger.info(f"会话延续测试: {'✅ 成功' if continuation_success else '❌ 失败'}")
        logger.info(f"总体结果: {success_count + (1 if continuation_success else 0)}/{total_tests + 1} 成功")
        
        if success_count == len(results) and continuation_success:
            logger.success("🎉 所有测试通过！Desktop ID 功能正常工作")
            return True
        else:
            logger.warning("⚠️ 部分测试失败，请检查日志")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        return False


if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    Desktop ID 功能测试                        ║
║                                                              ║
║  使用前请先配置:                                              ║
║  1. 修改 LOGIN_TOKEN 为您的有效登录令牌                       ║
║  2. 确认 BASE_URL 指向正确的API服务器                         ║
║  3. 确保API服务器正在运行                                     ║
║                                                              ║
║  测试内容:                                                    ║
║  - 不同类型的桌面ID (普通、AgentBay、None)                   ║
║  - 会话延续功能                                              ║
║  - STS Token 集成 (当传递桌面ID时)                           ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)
