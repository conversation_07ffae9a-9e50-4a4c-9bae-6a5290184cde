#!/usr/bin/env python3
"""
测试配置模板
请复制此文件为 test_config.py 并填写您的配置信息
"""

# ==================== API服务器配置 ====================
BASE_URL = "http://localhost:8000"  # API服务器地址

# ==================== 认证配置 ====================
# 请填写您的登录令牌
LOGIN_TOKEN = "YOUR_LOGIN_TOKEN_HERE"

# 可选：其他认证头信息
AUTH_HEADERS = {
    "sessionId": "test_session_id",
    "regionId": "cn-hangzhou"
}

# ==================== 测试数据配置 ====================

# 测试用的桌面ID列表
TEST_DESKTOP_IDS = [
    "desktop_001",           # 普通桌面ID
    "agentbay",             # AgentBay特殊桌面
    "desktop_test_123",     # 测试桌面ID
    "desktop_production",   # 生产环境桌面ID
    None                    # 不传递桌面ID的情况
]

# 测试用的Agent ID列表
TEST_AGENT_IDS = [
    "test_agent",
    "desktop_agent", 
    "chat_agent",
    "file_analyzer"
]

# 测试消息模板
TEST_MESSAGES = [
    "请帮我分析一下当前桌面环境",
    "启动AgentBay环境",
    "连接到指定的桌面实例",
    "这是一个测试消息",
    "请处理这个文件"
]

# ==================== 高级配置 ====================

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30.0

# 测试间隔时间（秒）
TEST_INTERVAL = 1.0

# 是否启用详细日志
VERBOSE_LOGGING = True

# 是否保存测试结果到文件
SAVE_RESULTS = True
RESULTS_FILE = "test_results.json"

# ==================== 使用说明 ====================
"""
使用步骤:
1. 复制此文件为 test_config.py
2. 填写 LOGIN_TOKEN 为您的有效登录令牌
3. 根据需要调整其他配置项
4. 运行测试脚本

示例:
cp test_config_template.py test_config.py
# 编辑 test_config.py 填写配置
python test_api_sessions_send.py
"""
