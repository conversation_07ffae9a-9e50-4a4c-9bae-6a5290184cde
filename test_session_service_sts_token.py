#!/usr/bin/env python3
"""
测试SessionService的STS Token功能
验证_build_runtime_resource方法中的STS Token获取和缓存功能
"""

import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.domain.services.session_service import SessionService
from src.domain.services.auth_service import AuthContext


def test_build_runtime_resource_with_desktop():
    """测试desktop类型的运行时资源构建"""
    logger.info("=== 测试desktop类型的运行时资源构建 ===")
    
    try:
        # 创建SessionService实例
        session_service = SessionService()
        
        # 创建测试用的AuthContext
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        # 测试参数
        desktop_id = "desktop_001"
        auth_code = "test_auth_code"
        
        # 模拟STS Token响应
        mock_sts_response = Mock()
        mock_sts_response.body = Mock()
        mock_sts_response.body.token = Mock()
        mock_sts_response.body.token.sts_token = "mock_sts_token_12345"
        mock_sts_response.body.token.session_id = "mock_session_id"
        
        # 模拟AppStream客户端
        with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client:
            mock_client = Mock()
            mock_client.get_sts_token.return_value = mock_sts_response
            mock_get_client.return_value = mock_client
            
            # 模拟Redis客户端
            with patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
                mock_redis = Mock()
                mock_redis.get.return_value = None  # 缓存未命中
                mock_redis.set.return_value = True
                mock_redis_class.return_value = mock_redis
                
                # 调用方法
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=desktop_id,
                    auth_code=auth_code,
                    auth_context=auth_context
                )
                
                # 验证结果
                assert runtime_resource is not None
                assert runtime_resource.type == "desktop"
                assert runtime_resource.token == "mock_sts_token_12345"  # 应该使用STS Token
                assert runtime_resource.cloud_resource_id == desktop_id
                
                # 验证AppStream客户端调用
                mock_client.get_sts_token.assert_called_once()
                call_args = mock_client.get_sts_token.call_args
                assert call_args[1]['end_user_id'] == auth_context.end_user_id
                assert call_args[1]['account_type'] == auth_context.account_type
                assert call_args[1]['user_ali_uid'] == auth_context.ali_uid
                
                # 验证策略JSON格式
                policy_arg = call_args[1]['policy']
                policy_dict = json.loads(policy_arg)
                expected_policy = {
                    "Version": "1",
                    "Resource": {
                        "Type": "Desktop",
                        "Id": desktop_id
                    }
                }
                assert policy_dict == expected_policy
                
                # 验证Redis缓存调用
                cache_key = f"sts_token:{auth_context.end_user_id}:{desktop_id}"
                mock_redis.get.assert_called_once_with(cache_key)
                mock_redis.set.assert_called_once_with(cache_key, "mock_sts_token_12345", ex=3600)
                
                logger.success("✅ desktop类型运行时资源构建测试通过")
                return True
                
    except Exception as e:
        logger.error(f"❌ desktop类型运行时资源构建测试失败: {e}")
        return False


def test_build_runtime_resource_with_cache():
    """测试使用缓存的STS Token"""
    logger.info("=== 测试使用缓存的STS Token ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        desktop_id = "desktop_002"
        auth_code = "test_auth_code"
        cached_token = "cached_sts_token_67890"
        
        # 模拟Redis客户端返回缓存的Token
        with patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
            mock_redis = Mock()
            mock_redis.get.return_value = cached_token  # 缓存命中
            mock_redis_class.return_value = mock_redis
            
            # 模拟AppStream客户端（不应该被调用）
            with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client:
                mock_client = Mock()
                mock_get_client.return_value = mock_client
                
                # 调用方法
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=desktop_id,
                    auth_code=auth_code,
                    auth_context=auth_context
                )
                
                # 验证结果
                assert runtime_resource is not None
                assert runtime_resource.type == "desktop"
                assert runtime_resource.token == cached_token  # 应该使用缓存的Token
                assert runtime_resource.cloud_resource_id == desktop_id
                
                # 验证缓存调用
                cache_key = f"sts_token:{auth_context.end_user_id}:{desktop_id}"
                mock_redis.get.assert_called_once_with(cache_key)
                
                # 验证AppStream客户端没有被调用
                mock_client.get_sts_token.assert_not_called()
                
                logger.success("✅ 缓存STS Token测试通过")
                return True
                
    except Exception as e:
        logger.error(f"❌ 缓存STS Token测试失败: {e}")
        return False


def test_build_runtime_resource_non_desktop():
    """测试非desktop类型的运行时资源构建"""
    logger.info("=== 测试非desktop类型的运行时资源构建 ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        # 测试agentbay类型
        runtime_resource = session_service._build_runtime_resource(
            desktop_id="agentbay",
            auth_code="test_auth_code",
            auth_context=auth_context
        )
        
        assert runtime_resource is not None
        assert runtime_resource.type == "agentbay"
        assert runtime_resource.token == "test_auth_code"  # 应该使用原始auth_code
        assert runtime_resource.cloud_resource_id is None
        
        # 测试none类型
        runtime_resource = session_service._build_runtime_resource(
            desktop_id=None,
            auth_code="test_auth_code",
            auth_context=auth_context
        )
        
        assert runtime_resource is not None
        assert runtime_resource.type == "none"
        assert runtime_resource.token == "test_auth_code"
        assert runtime_resource.cloud_resource_id is None
        
        logger.success("✅ 非desktop类型运行时资源构建测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 非desktop类型运行时资源构建测试失败: {e}")
        return False


def test_sts_token_error_handling():
    """测试STS Token获取错误处理"""
    logger.info("=== 测试STS Token获取错误处理 ===")
    
    try:
        session_service = SessionService()
        
        auth_context = AuthContext(
            ali_uid=****************,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="ALIYUN"
        )
        
        desktop_id = "desktop_003"
        auth_code = "test_auth_code"
        
        # 模拟AppStream客户端抛出异常
        with patch('src.domain.services.session_service.get_appstream_inner_client') as mock_get_client:
            mock_client = Mock()
            mock_client.get_sts_token.side_effect = Exception("API调用失败")
            mock_get_client.return_value = mock_client
            
            # 模拟Redis客户端
            with patch('src.domain.services.session_service.RedisClient') as mock_redis_class:
                mock_redis = Mock()
                mock_redis.get.return_value = None  # 缓存未命中
                mock_redis_class.return_value = mock_redis
                
                # 调用方法
                runtime_resource = session_service._build_runtime_resource(
                    desktop_id=desktop_id,
                    auth_code=auth_code,
                    auth_context=auth_context
                )
                
                # 验证结果 - 应该回退到使用auth_code
                assert runtime_resource is not None
                assert runtime_resource.type == "desktop"
                assert runtime_resource.token == auth_code  # 应该回退到auth_code
                assert runtime_resource.cloud_resource_id == desktop_id
                
                logger.success("✅ STS Token错误处理测试通过")
                return True
                
    except Exception as e:
        logger.error(f"❌ STS Token错误处理测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始SessionService STS Token功能测试...")
    
    success_count = 0
    total_tests = 4
    
    # 测试desktop类型资源构建
    if test_build_runtime_resource_with_desktop():
        success_count += 1
        logger.success("✅ desktop类型资源构建测试通过")
    else:
        logger.error("❌ desktop类型资源构建测试失败")
    
    # 测试缓存功能
    if test_build_runtime_resource_with_cache():
        success_count += 1
        logger.success("✅ 缓存功能测试通过")
    else:
        logger.error("❌ 缓存功能测试失败")
    
    # 测试非desktop类型
    if test_build_runtime_resource_non_desktop():
        success_count += 1
        logger.success("✅ 非desktop类型测试通过")
    else:
        logger.error("❌ 非desktop类型测试失败")
    
    # 测试错误处理
    if test_sts_token_error_handling():
        success_count += 1
        logger.success("✅ 错误处理测试通过")
    else:
        logger.error("❌ 错误处理测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有SessionService STS Token功能测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
