#!/usr/bin/env python3
"""
简化版 environments/list 测试脚本
专门测试 /api/environments/list 接口
"""

import asyncio
import httpx
import json
from loguru import logger
import sys

# ==================== 配置区域 ====================
# 尝试导入配置文件，如果不存在则使用默认配置

try:
    from test_config import *
    logger.info("✅ 已加载 test_config.py 配置文件")
except ImportError:
    logger.warning("⚠️ 未找到 test_config.py，使用默认配置")
    BASE_URL = "http://localhost:8000"
    LOGIN_TOKEN = "test-login-token-123"
    REQUEST_TIMEOUT = 30.0

# ==================== 测试代码 ====================

async def check_server_status():
    """检查服务器状态"""
    logger.info("=== 检查服务器状态 ===")

    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 尝试访问健康检查端点
            health_url = f"{BASE_URL}/health"
            response = await client.get(health_url)

            if response.status_code == 200:
                logger.success("✅ 服务器运行正常")
                return True
            else:
                logger.warning(f"⚠️ 健康检查返回状态码: {response.status_code}")
                return False

    except Exception as e:
        logger.error(f"❌ 服务器连接失败: {e}")
        logger.error("请确保服务器已启动: python -m uvicorn src.main:app --reload")
        return False


async def test_environments_list(login_token=None, session_id=None, region_id=None):
    """测试获取执行环境列表"""
    url = f"{BASE_URL}/api/environments/list"

    # 构建查询参数
    params = {}
    if login_token:
        params["loginToken"] = login_token
    if session_id:
        params["sessionId"] = session_id
    if region_id:
        params["regionId"] = region_id

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    logger.info(f"发送请求: {url}")
    logger.info(f"查询参数: {json.dumps(params, indent=2, ensure_ascii=False)}")

    try:
        async with httpx.AsyncClient(timeout=10.0) as client:  # 减少超时时间
            response = await client.get(url, params=params, headers=headers)

            logger.info(f"响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.success(f"✅ 请求成功")
                logger.info(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")

                # 解析环境数据
                data_field = result.get("Data") or result.get("data")
                if data_field:
                    environments = data_field.get("environments", [])
                    total = data_field.get("total", 0)
                    logger.info(f"环境总数: {total}")

                    for i, env in enumerate(environments, 1):
                        name = env.get("name", "未知")
                        desktop_id = env.get("desktop_id", "无")
                        status = env.get("desktop_status", "未知")
                        logger.info(f"  {i}. {name} (ID: {desktop_id}, 状态: {status})")

                return True

            else:
                logger.error(f"❌ 请求失败: {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return False

    except Exception as e:
        logger.error(f"❌ 请求异常: {e}")
        return False


async def test_different_tokens():
    """测试不同的登录令牌"""
    logger.info("=== 测试不同的登录令牌 ===")
    
    test_cases = [
        {
            "name": "默认测试令牌",
            "login_token": LOGIN_TOKEN,
            "session_id": None,
            "region_id": None
        },
        {
            "name": "带会话ID和区域ID",
            "login_token": LOGIN_TOKEN,
            "session_id": "test-session-123",
            "region_id": "cn-hangzhou"
        },
        {
            "name": "空令牌测试",
            "login_token": "",
            "session_id": None,
            "region_id": None
        },
        {
            "name": "无效令牌测试",
            "login_token": "invalid-token-xyz",
            "session_id": None,
            "region_id": None
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n{i}. {test_case['name']}")
        logger.info("-" * 40)
        
        success = await test_environments_list(
            login_token=test_case['login_token'],
            session_id=test_case['session_id'],
            region_id=test_case['region_id']
        )
        
        results.append({
            "name": test_case['name'],
            "success": success
        })
        
        # 测试间隔
        await asyncio.sleep(1)
    
    # 总结结果
    logger.info("\n" + "="*50)
    logger.info("测试结果总结:")
    
    for i, result in enumerate(results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        logger.info(f"{i}. {result['name']} - {status}")
    
    success_count = sum(1 for r in results if r["success"])
    logger.info(f"\n成功: {success_count}/{len(results)}")
    
    return results


async def test_with_headers():
    """测试使用请求头传递参数"""
    logger.info("\n=== 测试使用请求头传递参数 ===")
    
    url = f"{BASE_URL}/api/environments/list"
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Login-Token": LOGIN_TOKEN,
        "X-Session-Id": "header-session-456",
        "X-Region-Id": "cn-beijing"
    }
    
    logger.info(f"发送请求: {url}")
    logger.info(f"请求头: {json.dumps({k: v for k, v in headers.items() if k.startswith('X-')}, indent=2)}")
    
    try:
        async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
            response = await client.get(url, headers=headers)
            
            logger.info(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.success(f"✅ 请求头方式成功")
                logger.info(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return True
            else:
                logger.error(f"❌ 请求头方式失败: {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 请求头方式异常: {e}")
        return False


def check_configuration():
    """检查配置"""
    logger.info("=== 检查配置 ===")
    
    logger.info(f"API地址: {BASE_URL}")
    logger.info(f"登录令牌: {LOGIN_TOKEN}")
    logger.info(f"请求超时: {REQUEST_TIMEOUT}秒")
    logger.success("✅ 配置检查通过")
    return True


async def main():
    """主函数"""
    logger.info("🚀 开始 environments/list 接口测试")

    # 检查配置
    if not check_configuration():
        return False

    # 检查服务器状态
    if not await check_server_status():
        logger.error("❌ 服务器不可用，测试终止")
        return False

    try:
        # 只进行一个简单的测试
        logger.info("\n=== 执行基本测试 ===")
        success = await test_environments_list(login_token=LOGIN_TOKEN)

        # 最终总结
        logger.info("\n" + "="*60)
        logger.info("🎯 测试总结")

        if success:
            logger.success("🎉 environments/list 接口测试成功！")
            return True
        else:
            logger.warning("⚠️ 接口测试失败，请检查认证和权限")
            return False

    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        return False


if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════╗
║                environments/list 接口测试                     ║
║                                                              ║
║  使用前请先配置:                                              ║
║  1. 修改 LOGIN_TOKEN 为您的有效登录令牌                       ║
║  2. 确认 BASE_URL 指向正确的API服务器                         ║
║  3. 确保API服务器正在运行                                     ║
║                                                              ║
║  测试内容:                                                    ║
║  - 不同类型的登录令牌                                         ║
║  - 查询参数和请求头两种传参方式                               ║
║  - 会话ID和区域ID参数                                         ║
║  - 错误处理和响应解析                                         ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)
